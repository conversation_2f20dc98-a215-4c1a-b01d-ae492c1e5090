## Sua Marca Tem Valor — Documentação

### Visão Geral
Aplicação Next.js (App Router) com dois modos:
- Modo Visitante (online): rota `/` → tela inicial, depois `/quiz` sem PWA/IndexedDB.
- <PERSON><PERSON> (staff/offline): rota `/kiosk` → quiz offline-first com PWA, IndexedDB e sincronização automática.

Backend expõe APIs para perguntas e leads. Leads são gravados no Postgres (Prisma) e também enviados para Google Sheets (aba `RAW`).

### Estrutura de Diretórios
- `src/app/` — rotas Next (app router)
  - `/` raiz (visitante, online)
  - `/quiz` (form online-only)
  - `/kiosk` (quiz offline com PWA)
  - `/kiosk/offline` (tela staff com PIN, pendentes, sincronização manual)
  - `/api/leads` (POST para salvar e enviar ao Sheets; GET para listar)
  - `/api/perguntas` (GET perguntas)
  - `/api/kiosk/login|logout|me` (autenticação do kiosk via PIN)
- `src/components/` — UI (Quiz, telas, PWA, etc.)
- `src/hooks/` — hooks (offline, SW, formulário)
- `src/lib/` — lógica (calculations, IndexedDB, sync-manager, Google Sheets)
- `public/` — assets, manifest, service worker `sw-custom.js`
- `prisma/` — schema e seeds

### Variáveis de Ambiente (env.example)
Crie `.env` com:

```
DATABASE_URL="********************************/db?schema=public"
GOOGLE_SHEETS_ID="<ID da planilha>"
GOOGLE_APPLICATION_CREDENTIALS_SHEETS="./secrets/credenciais.json"
KIOSK_PIN="<PIN do kiosk>"
KIOSK_JWT_SECRET="<uma-chave-secreta>"
```

### PWA e Offline
- SW (`sw-custom.js`) precacheia `/kiosk` e `/kiosk/offline`. Fallback offline retorna `/kiosk`.
- Manifest `start_url` = `/kiosk`.
- SW registra apenas no modo kiosk (ou instalado).

### IndexedDB (Dexie)
- `leads` (fila offline com status `pendente|sincronizado`)
- `perguntas` (cache de perguntas para renderização offline)

### Sincronização
- `SyncManager` faz: check de conectividade, sincroniza pendentes (`/api/leads`), retry, auto-sync em `online`, intervalos e mensagem do SW.
- `/kiosk/offline` permite sincronização manual e mostra pendências/sincronizados.

### Google Sheets
- `src/lib/google-sheets.ts` (`GoogleSheetsService.appendRow`)
- Aba padrão: `RAW`
- Linha enviada: Data/Hora (America/Sao_Paulo), LeadID, Valor Estimado (R$), NomeMarca, Nome, WhatsApp, Email, Q5..Q16 (texto/formatado)

### Perguntas e Cálculo de Valuation
Conforme `prisma/seed.ts` e `regra_negocio_quiz.md`:
- Perguntas 1-4: dados pessoais (não pontuam)
- 5-6: radio (não pontuam)
- 7-9-12: numéricas (faturamento 25/26, margem %, seguidores)
- 10-11-13-16: radio (pontuam conforme opções)

Perguntas (5–16) e opções:
- 5. Qual define seu negócio? [Vestuário | Fabricação | Comércio | Serviços | Software/Tecnologia]
- 6. Tempo de marca? [<1 ano | 1–3 | 3–5 | >5]
- 7. Previsão de faturamento 2025 (R$) — NUMBER
- 8. Meta de faturamento 2026 (R$) — NUMBER
- 9. Margem de lucro (%) — NUMBER
- 10. Reconhecimento: [1 Quase ninguém | 2 Algumas pessoas | 3 Segmento reconhece | 4 Bastante conhecida]
- 11. Presença digital (evolutiva): [1 WhatsApp/Instagram | 2 + Site | 3 + Email | 4 + Mídia paga]
- 12. Seguidores totais — NUMBER
- 13. Base ativa de clientes: [1 Início | 2 Razoável | 3 Recorrentes | 4 Evangelizadores]
- 14. Processo de desenvolvimento: [1 Próprio/família | 2 Logo simples | 3 Agência/Especialista]
- 15. Registro INPI: [0 Não | 1 Andamento | 4 Certificado]
- 16. Investimento branding/marketing: [1 <1k | 2 1k–10k | 3 10k–50k | 4 50k–100k | 5 >100k]

`src/lib/calculations.ts`:
- Calcula `pontuacaoTotal` (soma de radios + fórmula de seguidores: seguidores/10000 + 1)
- `percentualFator` via `obterPercentualPorPontuacao` (com teto conforme regra)
- Projeção 10 anos (FCI, desaceleração), `lucroTotal10Anos`
- Valor da marca = `lucroTotal10Anos * percentualFator`

Cabeçalho sugerido na aba RAW (19 colunas):
`Data/Hora | LeadID | Valor Estimado | NomeMarca | Nome | WhatsApp | Email | Q5 | Q6 | Q7 | Q8 | Q9 | Q10 | Q11 | Q12 | Q13 | Q14 | Q15 | Q16`

### Rodando localmente
1. `npm install`
2. Configure `.env` e `./secrets/<credenciais>.json`
3. `npx prisma migrate dev && npx prisma generate`
4. `npm run dev`

### Deploy
- Definir envs no ambiente de produção.
- Garantir HTTPS (PWA exige).
- Abrir `/kiosk` em cada dispositivo (online) antes do evento para preencher cache inicial.

# 🏆 PWA Sua Marca Tem Valor - Documentação Completa
@c:\Users\<USER>\projects\pwa-sua-marca-tem-valor/src\components\forms\MultiStepQuizForm.tsx
Amigo, me responda sempre em português.

Estamos trabalhando no formulário dessa aplicação. 

Quero que analise os componentes e principalmente a lógica de persistencia e salvamento das resposta no indexDB.

Problema que precisamos identificar a causa e corrigir:
Estamos com um problema nas perguntas 7, 8, 9 e 10. 
Aparentemente o input de texto 'currency' da pergunta 7 está sendo preenchido com o id da resposta da pergunta 6. as demais perguntas depois dessa assumem um valor misteerioso nos inputs.

Preciso que revise sistematicamente a lógica de perguntas, respostas e persistencia dos dados no indexdb

## 📋 **RESUMO DO PROJETO**

Sistema PWA (Progressive Web App) para avaliação profissional de marcas, desenvolvido em **Next.js 15** com TypeScript. Funciona 100% offline com sincronização automática ao voltar online.

### ✅ **STATUS ATUAL: 100% FUNCIONAL + INTEGRAÇÕES**
- ✅ PWA completo e installable
- ✅ Funcionamento offline total
- ✅ Admin panel operacional  
- ✅ Build de produção funcionando
- ✅ Deploy-ready na Vercel
- ✅ **Integrações CRM/RD Station implementadas**
- ✅ **Dashboard de integrações completo**
- ✅ **Webhooks configurados**

---

## 🛠️ **STACK TECNOLÓGICA COMPLETA**

### **Frontend Core**
- **Next.js 15.4.5** - Framework React com App Router
- **TypeScript** - Tipagem estática
- **TailwindCSS** - Estilização utility-first
- **React Hook Form** - Gerenciamento de formulários
- **React Hot Toast** - Notificações elegantes

### **PWA & Offline**
- **next-pwa** - Progressive Web App features
- **Dexie.js** - IndexedDB wrapper para armazenamento offline
- **Service Worker customizado** - Cache inteligente e background sync

### **Backend & Database**
- **Prisma ORM** - Database toolkit com PostgreSQL
- **PostgreSQL** - Banco de dados relacional
- **Next.js API Routes** - Backend serverless

### **Performance & UX**
- **Sharp** - Processamento de imagens
- **Raleway Font** - Tipografia Google Fonts
- **Lazy Loading** - Carregamento otimizado
- **Background Sync** - Sincronização em background

### **Deploy & Produção**
- **Vercel** - Plataforma de deploy
- **ESLint + TypeScript** - Quality assurance
- **Security Headers** - Proteção em produção

---

## 📁 **ESTRUTURA DO PROJETO COMPLETA**

```
pwa-sua-marca-tem-valor/
├── 📄 Arquivos de Configuração
│   ├── package.json              # Dependencies e scripts
│   ├── next.config.js            # Configuração Next.js + PWA
│   ├── tailwind.config.ts        # Configuração TailwindCSS
│   ├── tsconfig.json             # Configuração TypeScript
│   ├── .eslintrc.json            # Regras de linting
│   ├── vercel.json               # Configuração deploy Vercel
│   └── README-DEPLOY.md          # Guia completo de deploy
│
├── 🗄️ Database & Schemas
│   ├── prisma/
│   │   ├── schema.prisma         # Schema do banco (Pergunta, Lead, etc)
│   │   └── seed.ts               # Dados iniciais (perguntas do quiz)
│   ├── exemplo.modelo.json       # Perguntas originais do quiz
│   └── pontuacao_fator.json      # Mapeamento pontuação → percentual
│
├── 🌐 Public Assets
│   ├── public/
│   │   ├── manifest.json         # PWA manifest otimizado
│   │   ├── sw.js                 # Service Worker avançado
│   │   ├── logo.svg              # Logo principal
│   │   ├── icons/                # Ícones PWA (72px-512px + splash iOS)
│   │   └── screenshots/          # Screenshots PWA (futuro)
│
├── 🏗️ App Structure (Next.js 15 App Router)
│   └── src/
│       ├── app/                  # Rotas e páginas
│       │   ├── layout.tsx        # Layout raiz com PWA meta tags
│       │   ├── page.tsx          # Página principal com quiz
│       │   ├── globals.css       # Estilos globais + variáveis CSS
│       │   ├── admin/            # Painel administrativo
│       │   │   ├── page.tsx      # Dashboard admin
│       │   │   ├── login/        # Página de login admin
│       │   │   ├── leads/        # Gestão de leads
│       │   │   └── perguntas/    # CRUD de perguntas
│       │   └── api/              # Backend API Routes
│       │       ├── leads/        # API de leads (POST)
│       │       ├── perguntas/    # API de perguntas (GET)
│       │       └── admin/        # APIs administrativas
│       │
│       ├── components/           # Componentes React organizados
│       │   ├── ui/               # Componentes base reutilizáveis
│       │   │   ├── Button.tsx    # Botão estilizado
│       │   │   ├── Input.tsx     # Input com validação
│       │   │   ├── RadioGroup.tsx # Radio buttons customizados
│       │   │   ├── StepButton.tsx # Botões do quiz com animação
│       │   │   ├── SyncStatus.tsx # Indicador de sincronização
│       │   │   ├── OfflineIndicator.tsx # Status online/offline
│       │   │   └── LazyImage.tsx # Imagens com lazy loading
│       │   ├── forms/            # Componentes de formulário
│       │   │   ├── MultiStepQuizForm.tsx # Formulário principal
│       │   │   └── PingIconSvg.tsx # Ícone de transição
│       │   ├── layout/           # Componentes de layout
│       │   │   └── Header.tsx    # Cabeçalho fixo
│       │   ├── admin/            # Componentes administrativos
│       │   │   ├── AdminLayout.tsx # Layout do admin
│       │   │   └── AdminSync.tsx # Sync status para admin
│       │   ├── common/           # Componentes comuns
│       │   │   └── PWAInstallPrompt.tsx # Prompt instalação PWA
│       │   └── debug/            # Ferramentas de debug
│       │       └── IndexedDBDebugger.tsx # Debug IndexedDB
│       │
│       ├── hooks/                # Custom React Hooks
│       │   ├── useQuizForm.ts    # Lógica do formulário quiz
│       │   ├── useOfflineStatus.ts # Detectar online/offline
│       │   ├── useServiceWorker.ts # Interação com SW
│       │   ├── useAdminAuth.ts   # Autenticação admin
│       │   └── useAdminData.ts   # Dados administrativos
│       │
│       ├── lib/                  # Bibliotecas e utilitários
│       │   ├── indexeddb.ts      # Configuração Dexie + OfflineService
│       │   ├── sync-manager.ts   # Gerenciador de sincronização
│       │   ├── sync.ts           # Serviços de sincronização
│       │   ├── calculations.ts   # Cálculos de valor da marca
│       │   ├── auth.ts           # Autenticação simples
│       │   ├── db.ts             # Cliente Prisma
│       │   ├── utils.ts          # Utilitários gerais
│       │   ├── performance.ts    # Otimizações de performance
│       │   └── integrations.ts   # Integrações CRM/RD (preparado)
│       │
│       └── types/                # Definições TypeScript
│           └── index.ts          # Interfaces principais
│
└── 🔧 Scripts & Tools
    └── scripts/
        └── generate-icons.js     # Gerador de ícones PWA
```

---

## 🔑 **FUNCIONALIDADES IMPLEMENTADAS**

### **1. Quiz de Avaliação de Marca**
- **Multi-step form** fluído (uma pergunta por vez)
- **Campos fixos:** Nome da Marca, Nome, WhatsApp, Email
- **Perguntas dinâmicas** carregadas do banco
- **Cálculo automático:** Pontuação → Percentual → Valor estimado
- **Visual feedback** em botões (escala + opacidade)
- **Transições animadas** entre perguntas

### **2. Funcionalidade Offline Completa**
- **Offline-first strategy** - sempre salva local primeiro
- **IndexedDB** para persistência (leads + perguntas)
- **Service Worker** com cache inteligente por tipo de recurso
- **Background sync** automático ao voltar online
- **Queue de requisições** para envios offline

### **3. Painel Administrativo**
- **Autenticação simples** (user: admin, pass: admin123)
- **Dashboard** com estatísticas e gráficos
- **CRUD completo** de perguntas e opções
- **Gestão de leads** com filtros e busca
- **Funciona offline** com sincronização

### **4. PWA Profissional**
- **Installable** em todos dispositivos
- **15 ícones** gerados automaticamente (72px-512px)
- **Splash screens** para iOS
- **Manifest otimizado** com metadados completos
- **Service Worker avançado** com 5 estratégias de cache

### **5. Performance Otimizada**
- **Lazy loading** de componentes e imagens
- **Bundle splitting** automático
- **Font optimization** (Raleway)
- **Cache strategies** inteligentes
- **Performance monitoring** built-in

### **6. Integrações Profissionais**
- **RD Station API** com campos customizados
- **CRM Genérico** com formatters especializados
- **Webhooks bidirecionais** para sincronização
- **Dashboard de monitoramento** com estatísticas
- **Sistema de retry** para falhas de integração
- **Processamento em background** sem travamento

---

## 🚀 **COMO RODAR O PROJETO**

### **1. Instalação**
```bash
# Clone ou navegue para o diretório
cd pwa-sua-marca-tem-valor

# Instalar dependências
npm install

# Configurar banco (ver seção Database)
```

### **2. Configuração do Banco**
```bash
# 1. Configurar DATABASE_URL no .env
echo 'DATABASE_URL="postgresql://user:password@localhost:5432/marcavalor"' > .env

# 2. Aplicar schema
npx prisma db push

# 3. Popular com dados iniciais
npx prisma db seed

# 4. (Opcional) Abrir Prisma Studio
npx prisma studio
```

### **3. Desenvolvimento**
```bash
# Servidor de desenvolvimento
npm run dev
# Acesse: http://localhost:3000

# Gerar ícones PWA
npm run generate:icons

# Build de produção
npm run build

# Servidor de produção
npm run start
```

### **4. Scripts Disponíveis**
```bash
npm run dev           # Desenvolvimento com Turbopack
npm run build         # Build otimizado
npm run start         # Servidor produção
npm run lint          # Linting
npm run db:push       # Aplicar schema Prisma
npm run db:seed       # Popular banco inicial
npm run db:studio     # Prisma Studio
npm run db:generate   # Gerar cliente Prisma
npm run generate:icons # Gerar ícones PWA
npm run build:pwa     # Build PWA completo
```

---

## 🗄️ **BANCO DE DADOS (PostgreSQL)**

### **Schema Prisma (prisma/schema.prisma)**
```prisma
// 4 tabelas principais:

model Pergunta {
  id            String    @id @default(cuid())
  texto         String
  tipo          String    @default("radio")
  multiplicador Boolean   @default(false)
  ordem         Int
  opcoes        OpcaoResposta[]
  respostas     RespostaLead[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model OpcaoResposta {
  id          String    @id @default(cuid())  
  label       String
  valor       Int
  pergunta    Pergunta  @relation(fields: [perguntaId], references: [id])
  perguntaId  String
  respostas   RespostaLead[]
  createdAt   DateTime  @default(now())
}

model Lead {
  id              String    @id @default(cuid())
  nomeMarca       String
  nome            String
  whatsapp        String
  email           String
  pontuacaoTotal  Int
  percentualFator Float
  valorEstimado   Float
  rdstationId     String?   // Para integração RD Station
  crmId           String?   // Para integração CRM
  respostas       RespostaLead[]
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model RespostaLead {
  id              String        @id @default(cuid())
  lead            Lead          @relation(fields: [leadId], references: [id])
  leadId          String
  pergunta        Pergunta      @relation(fields: [perguntaId], references: [id])
  perguntaId      String
  opcaoResposta   OpcaoResposta @relation(fields: [opcaoRespostaId], references: [id])
  opcaoRespostaId String
}
```

### **Dados Iniciais (prisma/seed.ts)**
- **9 perguntas** do quiz original
- **Opções de resposta** com valores numéricos
- **1 pergunta multiplicadora** para faturamento
- **Mapeamento pontuação → percentual** (pontuacao_fator.json)

---

## 🔧 **CONFIGURAÇÕES IMPORTANTES**

### **next.config.js**
```javascript
const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
  sw: 'sw.js', // Service Worker customizado
})

const nextConfig = {
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  poweredByHeader: false,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
}
```

### **Service Worker (public/sw.js)**
- **5 estratégias de cache:** CacheFirst, NetworkFirst, StaleWhileRevalidate
- **Cache TTL configurável** por tipo de recurso
- **Background sync** para leads offline
- **Queue system** para requisições pendentes
- **Error handling** robusto

### **IndexedDB (src/lib/indexeddb.ts)**
```typescript
// 2 tabelas offline:
interface ILeadOffline {
  id?: number
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  respostas: IRespostaOffline[]
  pontuacaoTotal: number
  percentualFator: number
  valorEstimado: number
  rdstationId?: string | null
  crmId?: string | null
  status: 'pendente' | 'sincronizado'
  createdAt: Date
}

interface IPerguntaOffline {
  id: string
  texto: string
  tipo: string
  multiplicador: boolean
  ordem: number
  opcoes: IOpcaoRespostaOffline[]
}
```

---

## 🎨 **DESIGN SYSTEM**

### **Cores Principais**
```css
:root {
  --primary-color: #269AB6;      /* Azul principal */
  --primary-hover: #1e7a94;      /* Azul hover */
  --text-primary: #1f2937;       /* Texto principal */
  --text-secondary: #6b7280;     /* Texto secundário */  
  --text-muted: #9ca3af;         /* Texto desbotado */
}
```

### **Tipografia**
- **Fonte:** Raleway (Google Fonts)
- **Pesos:** 300, 400, 500, 600, 700
- **Display:** swap para performance

### **Componentes Estilizados**
- **Botões:** Cor primária #269AB6 com hover
- **Inputs:** Contraste otimizado, placeholders visíveis  
- **Radio buttons:** Estilo de botões grandes
- **Animações:** Scale e opacity em interações

---

## 📱 **PWA FEATURES IMPLEMENTADAS**

### **Manifest (public/manifest.json)**
```json
{
  "name": "Sua Marca Tem Valor - Avaliação de Marca",
  "short_name": "MarcaValor", 
  "display": "standalone",
  "theme_color": "#269AB6",
  "categories": ["business", "productivity", "utilities"],
  "icons": [ /* 15 ícones de 72px a 512px */ ],
  "screenshots": [ /* Desktop e Mobile */ ]
}
```

### **Ícones PWA Completos**
- **8 ícones principais:** 72, 96, 128, 144, 152, 192, 384, 512px
- **7 splash screens iOS:** iPhone X, XR, XS Max, 8, 8 Plus, iPad, iPad Pro
- **Favicon otimizado:** favicon-pwa.png
- **Geração automática:** `npm run generate:icons`

### **Service Worker Avançado**
```javascript
// 5 estratégias implementadas:
- CACHE_FIRST: Arquivos estáticos, imagens
- NETWORK_FIRST: APIs, dados dinâmicos  
- STALE_WHILE_REVALIDATE: Páginas HTML
- NETWORK_ONLY: APIs de escrita
- CACHE_ONLY: Fallbacks offline
```

---

## 🔄 **SISTEMA DE SINCRONIZAÇÃO**

### **Offline-First Strategy**
1. **SEMPRE salva offline primeiro** (IndexedDB)
2. **Se online:** Tenta enviar para servidor também
3. **Se falhar:** Mantém na queue de sincronização  
4. **Background sync:** Processa queue automaticamente
5. **Reconexão:** Dispara sync completa

### **SyncManager (src/lib/sync-manager.ts)**
```typescript
// Funcionalidades principais:
- syncPendingLeads()     // Sincroniza leads offline
- syncQuestions()        // Atualiza perguntas do servidor
- fullSync()             // Sincronização completa
- getSyncStats()         // Estatísticas de sync
- Background sync events // Service Worker integration
```

### **Logs de Debug**
- **Console detalhado** em desenvolvimento
- **IndexedDBDebugger** component para testes
- **SyncStatus** component para usuário final
- **Performance monitoring** built-in

---

## 🛡️ **SEGURANÇA & PRODUÇÃO**

### **Headers de Segurança (vercel.json)**
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        { "key": "X-Content-Type-Options", "value": "nosniff" },
        { "key": "X-Frame-Options", "value": "DENY" },
        { "key": "X-XSS-Protection", "value": "1; mode=block" },
        { "key": "Referrer-Policy", "value": "origin-when-cross-origin" }
      ]
    }
  ]
}
```

### **Otimizações de Build**
- **ESLint/TypeScript:** Não bloqueia build em produção
- **Console.log:** Removido automaticamente em produção  
- **Bundle analysis:** Chunks otimizados
- **Static generation:** 14 páginas pré-renderizadas

---

## 🚀 **DEPLOY NA VERCEL**

### **Pré-requisitos**
1. **Banco PostgreSQL** (Vercel Postgres recomendado)
2. **Variável `DATABASE_URL`** configurada
3. **Vercel CLI** instalado (`npm i -g vercel`)

### **Deploy Passo-a-Passo**
```bash
# 1. Build local (teste)
npm run build:pwa

# 2. Deploy inicial  
vercel

# 3. Configurar variáveis de ambiente na dashboard Vercel:
#    DATABASE_URL="postgresql://..."

# 4. Setup banco
npx prisma db push
npx prisma db seed

# 5. Deploy final
vercel --prod
```

### **Validação Pós-Deploy**
- [ ] PWA installable no domínio
- [ ] Funciona offline corretamente
- [ ] Lighthouse score > 90
- [ ] Admin panel acessível
- [ ] Sincronização funcionando

---

## ✅ **FASE 6 CONCLUÍDA: INTEGRAÇÕES CRM/RD STATION**

### **✅ IMPLEMENTADO:**
- ✅ **Webhooks** RD Station e CRM (`/api/webhooks/`)
- ✅ **API integrada** (`/api/leads` processa integrações)
- ✅ **Dashboard completo** (`/admin/integracoes`)
- ✅ **Sistema de teste** e reprocessamento
- ✅ **Environment variables** estruturadas
- ✅ **Formatters especializados** (Pipedrive, HubSpot)

### **🔧 CONFIGURAÇÃO DAS INTEGRAÇÕES:**

#### **1. RD Station:**
```bash
# 1. Obter token da API em: https://rdstation.com/developers/
# 2. Configurar variável de ambiente:
RDSTATION_TOKEN="seu_token_aqui"

# 3. Webhook URL (configurar no RD Station):
https://brandvaluation.registre.se/api/webhooks/rdstation
```

#### **2. CRM Genérico:**
```bash
# Configurar API do seu CRM:
CRM_API_URL="https://api.seucrm.com"
CRM_API_KEY="sua_chave_api"

# Webhook URL (configurar no CRM):
https://brandvaluation.registre.se/api/webhooks/crm
```

#### **3. Campos Enviados:**
- **RD Station:** `email`, `name`, `mobile_phone`, `company`, `cf_custom_fields`
- **CRM:** `nome_marca`, `nome`, `whatsapp`, `email`, `pontuacao_total`, `percentual_fator`, `valor_estimado`

#### **4. Monitoramento:**
- **Dashboard:** `/admin/integracoes`
- **Teste:** Botão "🧪 Testar Integrações"
- **Reprocessar:** Botão para reprocessar falhas
- **Estatísticas:** Total integrado vs falhas

---

## 🔮 **PRÓXIMOS PASSOS (ROADMAP)**

### **Melhorias Futuras**
- [ ] **Push notifications** (Service Worker pronto)
- [ ] **Analytics avançado** (Google Analytics)
- [ ] **A/B testing** das perguntas
- [ ] **Export/import** de dados
- [ ] **Multi-tenancy** (múltiplas empresas)
- [ ] **Themes customizáveis**

### **Performance Avançada**
- [ ] **Edge functions** para geolocalização
- [ ] **CDN optimization** para assets
- [ ] **Server-side rendering** otimizado
- [ ] **Progressive enhancement**

---

## 🔍 **DEBUGGING & TROUBLESHOOTING**

### **Desenvolvimento**
```bash
# Debug IndexedDB
# Acesse: DevTools → Application → IndexedDB → MarcaValorDB

# Debug Service Worker  
# Acesse: DevTools → Application → Service Workers

# Debug Network
# Acesse: DevTools → Network → Disable cache para testar offline

# Componente debug (desenvolvimento only)
# Visível na página principal: IndexedDBDebugger
```

### **Logs Importantes**
```javascript
// No console:
🔄 Salvando lead offline primeiro...
✅ Lead salvo offline com ID: 1
🌐 Online - tentando sincronizar imediatamente...
✅ Lead sincronizado com servidor!
```

### **Problemas Comuns**
1. **Build error EPERM:** Remover `.next` folder
2. **Service Worker não registra:** Verificar HTTPS
3. **IndexedDB não funciona:** Verificar suporte no browser
4. **Sync não acontece:** Verificar online status
5. **PWA não instala:** Verificar manifest e ícones

---

## 📞 **SUPORTE & CONTATO**

### **Arquivos de Referência**
- **README-DEPLOY.md** - Guia completo de deploy
- **prisma/schema.prisma** - Schema do banco
- **public/manifest.json** - Configuração PWA
- **src/lib/sync-manager.ts** - Lógica de sincronização

### **Comandos de Emergência**
```bash
# Reset completo
rm -rf .next node_modules
npm install
npm run generate:icons
npm run build

# Reset banco
npx prisma db push --force-reset
npx prisma db seed

# Debug completo
npm run dev
# Abrir: http://localhost:3000
# Ver: Console + DevTools
```

---

## 🏆 **CONQUISTAS FINAIS**

### ✅ **100% Funcional**
- PWA profissional installable
- Offline-first com sync automática  
- Admin panel completo
- Performance otimizada
- Deploy-ready na Vercel
- Documentação completa

### 📊 **Métricas de Qualidade**
- **Build time:** 7 segundos
- **Bundle size:** Otimizado com chunks
- **Lighthouse ready:** PWA compliant
- **Security:** Headers configurados
- **Accessibility:** Contraste otimizado

---

**🎉 PROJETO 100% COMPLETO E DOCUMENTADO!**

**Agora você tem tudo necessário para continuar, fazer deploy ou expandir o projeto independentemente!** 🚀