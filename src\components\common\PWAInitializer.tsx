"use client"

import { useEffect } from 'react'
import { useServiceWorker } from '@/hooks/useServiceWorker'
import { SyncManager } from '@/lib/sync-manager'
import { IndexedDBTester } from '@/lib/indexeddb'

export function PWAInitializer() {
  const { isRegistered } = useServiceWorker()

  useEffect(() => {
    const initializePWA = async () => {
      console.log('🚀 Inicializando PWA...')
      
      // 1. Testar IndexedDB
      const dbWorking = await IndexedDBTester.testConnection()
      if (!dbWorking) {
        console.error('❌ IndexedDB não está funcionando!')
        return
      }
      
      // 2. Inicializar auto-sync
      SyncManager.startAutoSync()
      console.log('✅ Auto-sync iniciado')
      
      // 3. Sincronizar perguntas na inicialização
      if (typeof navigator !== 'undefined' && navigator.onLine) {
        console.log('🌐 Online - sincronizando perguntas...')
        await SyncManager.syncQuestions()
      }
      
      console.log('✅ PWA inicializado com sucesso')
    }

    // Aguardar Service Worker estar pronto
    if (isRegistered) {
      initializePWA()
    }
  }, [isRegistered])

  // Este componente não renderiza nada visível
  return null
}