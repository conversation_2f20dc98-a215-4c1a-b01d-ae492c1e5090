.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 780px;
  margin: 0 auto;
  /* Fallbacks antes do clamp() para navegadores que não suportam */
  padding-inline: 1rem;
  padding-inline: clamp(1rem, 4vw, 1.25rem);
  /* Fallback para navegadores sem env() */
  padding-block: 1rem;
  padding-block: clamp(1rem, 6vh, 3rem);
  /* Com safe-area para iOS */
  padding-top: calc(env(safe-area-inset-top) + 40px);
  /* Fallback do clamp() dentro do calc() */
  padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
  padding-bottom: calc(env(safe-area-inset-bottom) + clamp(2rem, 2vh, 3rem));
  /* Evita saltos quando a barra de endereço aparece (svh) */
  min-height: 100svh;
  min-height: 100dvh; /* fallback */
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* Logo Container */
.logoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 1.5rem;
}

/* BRAND VALUATION */
.brandValuationContainer {
  width: 100%;
  display: flex;
  justify-content: center;
}

.brandValuationText {
  font-family: var(--font-raleway);
  text-transform: uppercase;
  letter-spacing: .8rem; /* ~103% tracking (aproximação) */
  /* Fallback e depois clamp() */
  font-size: 1rem;
  font-size: clamp(0.75rem, 2vw, 1.5rem);
  font-weight: bold;
}

/* Título */
.titleContainer {
  text-align: center;
  width: 100%;
  margin-top: clamp(30px, 4.8vw, 68px); /* 78px abaixo de BRAND VALUATION */
}

.title {
  font-family: var(--font-raleway);
  font-weight: 800; /* Extrabold */
  /* Fallback e depois clamp() */
  font-size: 40px;
  font-size: clamp(40px, 6vw, 50px);
  line-height: 47px; /* ~94% */
  color: #FFFFFF;
  text-transform: uppercase; 
  margin: 0;
}

/* Subtítulo */
.subtitleContainer {
  width: 100%;
  text-align: center;
  margin-top: clamp(20px, 4.8vw, 30px); /* 30px abaixo do título */
}

.subtitle {
  margin: 0;
  font-family: var(--font-raleway);
  /* Fallback e depois clamp() */
  font-size: 20px;
  font-size: clamp(20px, 4.8vw, 25px);
  line-height: 32px; /* 128% */
  color: #202F21;
}

.subtitlePart1 {
  font-style: italic;
  font-weight: 400;
}

.subtitlePart2 {
  font-weight: 800;
  font-style: italic;
}

/* Prêmio */
.prizeImageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-shrink: 0; /* Impede compressão */
  margin-top: 38px; /* 38px abaixo do subtítulo */
}
.prizeImageContainer :global(img) {
  width: 276.13px !important;
  height: 257px !important;
}

/* Tagline (sem espaço entre a imagem e o texto) */
.taglineContainer {
  width: 100%;
  text-align: center;
  margin-top: 0; /* sem espaço */
}

.tagline {
  margin: 0; /* sem espaço entre a imagem e o texto */
  font-family: var(--font-raleway);
  font-weight: 400;
  /* Fallback e depois clamp() */
  font-size: 16px;
  font-size: clamp(18px, 2.8vw, 16px);
  color: #202F21;
}

/* Botão */
.buttonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 58px; /* 68px abaixo da tagline */
}

.ctaButton {
  width: 314px;
  height: 80px;
  background: #202F21;
  color: #FFFFFF;
  border: none;
  border-radius: 45px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px; /* ícone a 8px do texto */
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
}

.ctaLabel {
  font-family: var(--font-raleway);
  font-weight: bold;
  letter-spacing: 0.06em;
  /* Fallback e depois clamp() */
  font-size: 24px;
  font-size: clamp(24px, 2.8vw, 30px);
}

/* Responsividade */
@media (min-width: 768px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1200px; /* largura suficiente para evitar quebras */
  }

  .title {
    white-space: nowrap; /* sem quebra no desktop */
  }

  .subtitle {
    white-space: nowrap; /* sem quebra no desktop */
  }

  .subtitlePart2 {
    display: inline; /* no desktop, ficar na mesma linha */
  }
}

/* Alturas pequenas (ex.: ~667px) */
@media (max-height: 740px) {
  .container {
    justify-content: center; /* mantém centralização inteligente */
    /* compacta o padding mantendo safe-area */
    padding-top: calc(env(safe-area-inset-top) + 2rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
  }
}

/* Otimizações para mobile - barra de endereços */
@media (max-width: 767px) {
  /* gap e padding já são responsivos via clamp() */
  .subtitlePart2 { display: block; }
}

/* Para dispositivos com altura muito pequena */
@media (max-height: 600px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 0.5rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 0.5rem);
  }
}

/* Breakpoint para telas muito pequenas (largura ou altura) */
@media (max-width: 390px), (max-height: 857px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 48px);
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
  }

  .logoContainer :global(img) {
    width: 180px !important;
    height: auto !important;
  }

  .brandValuationText {
    font-size: 0.85rem;
  }

  .titleContainer {
    margin-top: 24px;
  }

  .title {
    font-size: 34px;
    line-height: 40px;
  }

  .subtitleContainer {
    margin-top: 14px;
  }

  .subtitle {
    font-size: 18px;
    line-height: 24px;
  }

  .prizeImageContainer {
    margin-top: 24px;
  }

  .prizeImageContainer :global(img) {
    width: 190px !important;
    height: auto !important;
  }

  .tagline {
    font-size: 15px;
  }

  .buttonContainer {
    margin-top: 30px;
  }

  .ctaButton {
    width: 260px;
    height: 64px;
    border-radius: 36px;
  }

  .ctaLabel {
    font-size: 20px;
  }
}