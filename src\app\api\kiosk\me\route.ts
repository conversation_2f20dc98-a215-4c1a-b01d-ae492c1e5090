import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

function base64urlDecode(input: string) {
  input = input.replace(/-/g, '+').replace(/_/g, '/')
  const pad = input.length % 4
  if (pad) input += '='.repeat(4 - pad)
  return Buffer.from(input, 'base64').toString('utf8')
}

function verify(token: string, secret: string) {
  const parts = token.split('.')
  if (parts.length !== 3) return false
  const [encHeader, encPayload, encSig] = parts
  const data = `${encHeader}.${encPayload}`
  const expected = crypto.createHmac('sha256', secret).update(data).digest()
  const expectedB64Url = Buffer.from(expected)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
  return crypto.timingSafeEqual(Buffer.from(encSig), Buffer.from(expectedB64Url))
}

export async function GET(request: NextRequest) {
  const token = request.cookies.get('kiosk_auth')?.value
  if (!token) {
    return NextResponse.json({ authenticated: false }, { status: 200 })
  }
  const secret = process.env.KIOSK_JWT_SECRET || ""
  const ok = verify(token, secret)
  return NextResponse.json({ authenticated: ok }, { status: 200 })
}


