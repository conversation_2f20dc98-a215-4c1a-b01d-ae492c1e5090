# Ajustes de UI/UX e Melhorias na Aplicação

## Contexto
Este documento descreve alterações visuais e funcionais para padronizar a interface, otimizar a usabilidade e melhorar a clareza das informações.  
O objetivo é alinhar a aplicação à identidade visual (100% Raleway), reduzir rolagem desnecessária e tornar a navegação mais intuitiva.

---

## 1. Padronização de Fontes
**Objetivo:**  
Garantir consistência tipográfica em toda a aplicação.  

**Tarefas:**  
- Aplicar a fonte **Raleway** em 100% dos elementos de texto da aplicação.
- Verificar compatibilidade de pesos (regular, medium, bold) para manter legibilidade.

---

## 2. Otimização da Página Inicial
**Objetivo:**  
Apresentar todo o conteúdo principal na primeira dobra, sem rolagem inicial.  

**Tarefas:**  
- Reduzir e reorganizar textos.
- Ajustar espaçamentos para enquadramento perfeito na primeira dobra.
- Testar em diferentes resoluções e dispositivos.

---

## 3. Imagem do Plaude
**Objetivo:**  
Fornecer duas versões de imagem para teste de preferência.  

**Tarefas:**  
public/images/plaud-ai-transparent - para fundo verde atual
public/images/plaud-ai - para fundo #00BFD6
- Criar duas variações:
  - Fundo azul #00BFD6.
  - Fundo verde atual.
- Adicionar **toggle** para alternar entre as versões.

---

## 4. Ajustes de Layout e Navegação
**Objetivo:**  
Melhorar alinhamento, padronização e responsividade da navegação.  

**Tarefas:**  
- Remover barra de progresso no topo do quiz.
- Garantir enquadramento sem rolagem vertical.
- Criar **toggle de posicionamento vertical**:
  - Opção 1: Centralizar verticalmente.
  - Opção 2: Alinhar ao topo com `padding`.
  - Considerar a barra de endereços visível no viewport.
- Centralizar texto dos botões de seleção de resposta.
---

## 5. Campos Interativos: Margem e Faturamento
**Objetivo:**  
Facilitar ajuste rápido e entrada manual de valores.  

**Tarefas:**  
- Criar componentes com:
  - Controle deslizante (swipe).
  - Campo aberto para digitação manual.
- Pesquisar e aplicar boas práticas de UX para inputs mistos.

---

## 6. Revisão de Textos
**Objetivo:**  
Melhorar clareza, consistência e gramática das perguntas.  

**Tarefas:**  
- Revisar ortografia e gramática.
- Uniformizar estilo e tom das perguntas.

---

## 7. Página de Edição de Conteúdo
**Objetivo:**  
Permitir gerenciamento completo das perguntas e pontuações.  

**Tarefas:**  
- Criar página para:
  - Editar perguntas.
  - Gerenciar opções de resposta.
  - Ajustar pontuação.

---

## 8. Novo Modo de Exibição das Perguntas
**Objetivo:**  
Melhorar experiência de resposta com exibição dinâmica e fluida.  

**Tarefas:**  
- Criar exibição em **slide vertical**:
  - Uma pergunta por vez.
  - Botão de resposta com ícone de estrela (indicando peso/pontuação).
  - Transição suave (smooth scrolling) entre perguntas.

---

## 9. Pergunta sobre Canais Digitais
**Objetivo:**  
Permitir múltipla escolha com pontuação acumulada.  

**Tarefas:**  
- Transformar em campo de múltipla escolha.
- Exibir opções separadas e selecionáveis.
- Calcular score acumulado com base nas escolhas.

---

## 10. Tela de Resultados
**Objetivo:**  
Transmitir de forma clara o valor e reconhecimento da marca.  

**Tarefas:**  
- Exibir valor da marca e cálculo do valuation.
- Mostrar quanto a marca representa no valuation total.
- Adicionar escala visual de reconhecimento, mostrando posição da marca em relação à tabela de pontuações.
- Adicionar **botão de compartilhamento** (redes sociais, PDF, etc.).

---

## Observações Finais
- Todas as alterações devem manter design responsivo.
- Priorizar carregamento rápido e performance.
- Manter consistência visual com o padrão atual do site.
    