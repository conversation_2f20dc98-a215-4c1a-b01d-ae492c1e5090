'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react'
import styles from './styles/VerticalSwipeOptions.module.css'
import { FaStar } from 'react-icons/fa'

export interface VerticalOption {
  id: string
  label: string
  valor: number
}

interface VerticalSwipeOptionsProps {
  options: VerticalOption[]
  selectedId?: string
  onSelect: (opcaoId: string, valor: number) => void
}

export default function VerticalSwipeOptions({ options, selectedId, onSelect }: VerticalSwipeOptionsProps) {
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [activeIndex, setActiveIndex] = useState<number>(0)

  const maxStars = useMemo(() => {
    const values = options.map(o => o.valor)
    const max = values.length ? Math.max(...values) : 5
    return Math.min(5, Math.max(1, max))
  }, [options])

  const initialIndex = useMemo(() => {
    if (!selectedId) return 0
    const idx = options.findIndex(o => o.id === selectedId)
    return idx >= 0 ? idx : 0
  }, [options, selectedId])

  useEffect(() => {
    setActiveIndex(initialIndex)
    const c = containerRef.current
    if (c) {
      const itemHeight = c.firstElementChild instanceof HTMLElement ? c.firstElementChild.offsetHeight : 0
      if (itemHeight > 0) {
        c.scrollTo({ top: initialIndex * itemHeight, behavior: 'instant' as any })
      }
    }
  }, [initialIndex])

  const handleScroll = () => {
    const c = containerRef.current
    if (!c) return
    const item = c.firstElementChild as HTMLElement | null
    if (!item) return
    const itemHeight = item.offsetHeight
    const idx = Math.round(c.scrollTop / Math.max(1, itemHeight))
    if (idx !== activeIndex) setActiveIndex(Math.max(0, Math.min(options.length - 1, idx)))
  }

  const handleSelectActive = () => {
    const opt = options[activeIndex]
    if (opt) onSelect(opt.id, opt.valor)
  }

  return (
    <div className={styles.wrapper}>
      <div className={styles.viewport}>
        <div
          ref={containerRef}
          className={styles.scroller}
          onScroll={handleScroll}
        >
          {options.map((opcao, index) => {
            const isActive = index === activeIndex
            return (
              <button
                key={opcao.id}
                type="button"
                className={styles.slide}
                onClick={() => onSelect(opcao.id, opcao.valor)}
              >
                <div className={styles.stars} aria-hidden>
                  {Array.from({ length: maxStars }).map((_, i) => (
                    <FaStar key={i} className={i < opcao.valor ? styles.starFilled : styles.starEmpty} />
                  ))}
                </div>
                <div className={styles.label}>{opcao.label}</div>
                {isActive && <div className={styles.activeHint}>Deslize para ver mais • Toque para selecionar</div>}
              </button>
            )
          })}
        </div>
      </div>
      <div className={styles.controls}>
        <button
          type="button"
          className={styles.primary}
          onClick={handleSelectActive}
        >
          Selecionar
        </button>
      </div>
    </div>
  )
}


