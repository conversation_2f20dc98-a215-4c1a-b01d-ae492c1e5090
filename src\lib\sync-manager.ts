"use client"

import { OfflineService, ILeadOffline } from './indexeddb'
import toast from 'react-hot-toast'

export type SyncStatus = 'idle' | 'syncing' | 'success' | 'error'

export interface SyncResult {
  success: number
  failed: number
  total: number
  errors: string[]
}

export class SyncManager {
  private static syncInProgress = false
  private static syncListeners: ((status: SyncStatus, result?: SyncResult) => void)[] = []
  private static retryQueue: ILeadOffline[] = []
  private static maxRetries = 3
  private static retryDelay = 2000

  /**
   * Adicionar listener para mudanças de status de sync
   */
  static addSyncListener(callback: (status: SyncStatus, result?: SyncResult) => void) {
    this.syncListeners.push(callback)
  }

  /**
   * Remover listener
   */
  static removeSyncListener(callback: (status: SyncStatus, result?: SyncResult) => void) {
    this.syncListeners = this.syncListeners.filter(cb => cb !== callback)
  }

  /**
   * Notificar listeners sobre mudança de status
   */
  private static notifyListeners(status: SyncStatus, result?: SyncResult) {
    this.syncListeners.forEach(callback => callback(status, result))
  }

  /**
   * Verificar se existe conexão com a internet
   */
  static async checkConnectivity(): Promise<boolean> {
    if (typeof navigator === 'undefined' || !navigator.onLine) return false

    try {
      // Testar conectividade real fazendo uma requisição
      const response = await fetch('/api/perguntas', {
        method: 'HEAD',
        cache: 'no-cache'
      })
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * Sincronizar leads pendentes com retry logic
   */
  static async syncPendingLeads(): Promise<SyncResult> {
    if (this.syncInProgress) {
      console.log('Sync já em progresso')
      return { success: 0, failed: 0, total: 0, errors: [] }
    }

    this.syncInProgress = true
    this.notifyListeners('syncing')

    const result: SyncResult = {
      success: 0,
      failed: 0,
      total: 0,
      errors: []
    }

    try {
      // Verificar conectividade
      const isOnline = await this.checkConnectivity()
      if (!isOnline) {
        throw new Error('Sem conexão com a internet')
      }

      // Buscar leads pendentes
      const pendingLeads = await OfflineService.getPendingLeads()
      result.total = pendingLeads.length

      if (result.total === 0) {
        this.notifyListeners('success', result)
        return result
      }

      console.log(`Iniciando sync de ${result.total} leads...`)

      // Processar cada lead
      for (const lead of pendingLeads) {
        try {
          const success = await this.syncSingleLead(lead)
          if (success) {
            result.success++
            if (lead.id) {
              await OfflineService.markLeadAsSynced(lead.id)
            }
          } else {
            result.failed++
            this.retryQueue.push(lead)
            result.errors.push(`Falha ao sincronizar lead: ${lead.nomeMarca}`)
          }
        } catch (error) {
          result.failed++
          this.retryQueue.push(lead)
          result.errors.push(`Erro no lead ${lead.nomeMarca}: ${error}`)
        }
      }

      // Processar fila de retry se necessário
      if (this.retryQueue.length > 0) {
        await this.processRetryQueue(result)
      }

      // Notificar resultado
      const status: SyncStatus = result.failed > 0 ? 'error' : 'success'
      this.notifyListeners(status, result)

      // Toast de feedback
      if (result.success > 0) {
        toast.success(`${result.success} leads sincronizados!`)
      }
      if (result.failed > 0) {
        toast.error(`${result.failed} leads falharam na sincronização`)
      }

      return result

    } catch (error) {
      console.error('Erro na sincronização:', error)
      result.errors.push(error instanceof Error ? error.message : 'Erro desconhecido')
      this.notifyListeners('error', result)
      toast.error('Erro ao sincronizar dados')
      return result
    } finally {
      this.syncInProgress = false
    }
  }

  /**
   * Sincronizar um único lead
   */
  private static async syncSingleLead(lead: ILeadOffline): Promise<boolean> {
    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nomeMarca: lead.nomeMarca,
          nome: lead.nome,
          whatsapp: lead.whatsapp,
          email: lead.email,
          respostas: lead.respostas.reduce((acc: Record<string, string | number | string[]>, resposta) => {
            // Suporte a diferentes tipos de resposta
            if (resposta.opcaoRespostaId) {
              acc[resposta.perguntaId] = resposta.opcaoRespostaId
            } else if (resposta.opcaoRespostaIds && resposta.opcaoRespostaIds.length > 0) {
              // Envia como array de strings
              acc[resposta.perguntaId] = resposta.opcaoRespostaIds
            } else if (resposta.valorNumerico !== undefined) {
              acc[resposta.perguntaId] = resposta.valorNumerico.toString()
            } else if (resposta.valorTexto) {
              acc[resposta.perguntaId] = resposta.valorTexto
            }
            return acc
          }, {} as { [key: string]: string }),
          // Dados de cálculo (compatibilidade)
          pontuacaoTotal: lead.pontuacaoTotal,
          percentualFator: lead.percentualFator,
          valorEstimado: lead.valorEstimado,
          // Novos campos opcionais
          faturamento2025: lead.faturamento2025,
          faturamento2026: lead.faturamento2026,
          margemLucro: lead.margemLucro,
          lucroTotal10Anos: lead.lucroTotal10Anos,
          rdstationId: lead.rdstationId,
          crmId: lead.crmId
        })
      })

      return response.ok
    } catch (error) {
      console.error('Erro ao sincronizar lead:', error)
      return false
    }
  }

  /**
   * Processar fila de retry
   */
  private static async processRetryQueue(result: SyncResult) {
    const retryLeads = [...this.retryQueue]
    this.retryQueue = []

    for (const lead of retryLeads) {
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          console.log(`Tentativa ${attempt}/${this.maxRetries} para lead: ${lead.nomeMarca}`)
          
          const success = await this.syncSingleLead(lead)
          
          if (success) {
            result.success++
            result.failed--
            if (lead.id) {
              await OfflineService.markLeadAsSynced(lead.id)
            }
            break // Sucesso, parar tentativas
          } else if (attempt < this.maxRetries) {
            // Aguardar antes da próxima tentativa
            await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt))
          }
        } catch (error) {
          console.error(`Tentativa ${attempt} falhou:`, error)
          if (attempt === this.maxRetries) {
            // Falha definitiva
            result.errors.push(`Lead ${lead.nomeMarca} falhou após ${this.maxRetries} tentativas`)
          }
        }
      }
    }
  }

  /**
   * Sincronizar perguntas do servidor
   */
  static async syncQuestions(): Promise<boolean> {
    try {
      const isOnline = await this.checkConnectivity()
      if (!isOnline) return false

      console.log('🔄 Sincronizando perguntas do servidor...')
      const response = await fetch('/api/perguntas')
      
      if (response.ok) {
        const perguntasApi = await response.json()
        console.log('📥 Perguntas recebidas da API:', perguntasApi.length)
        
        // Converter formato da API para formato do IndexedDB
        const perguntasOffline = perguntasApi.map((pergunta: any) => ({
          id: pergunta.id,
          texto: pergunta.texto,
          tipo: pergunta.tipo,
          multiplicador: pergunta.multiplicador,
          ordem: pergunta.ordem,
          config: pergunta.config ?? undefined,
          opcoes: pergunta.opcoes.map((opcao: any) => ({
            id: opcao.id,
            label: opcao.label,
            valor: opcao.valor
          }))
        }))
        
        await OfflineService.saveQuestions(perguntasOffline)
        console.log('✅ Perguntas sincronizadas offline:', perguntasOffline.length)
        return true
      } else {
        console.error('❌ Erro na resposta da API:', response.status, response.statusText)
      }
      
      return false
    } catch (error) {
      console.error('❌ Erro ao sincronizar perguntas:', error)
      return false
    }
  }

  /**
   * Sincronização completa (leads + perguntas)
   */
  static async fullSync(): Promise<SyncResult> {
    const isOnline = await this.checkConnectivity()
    if (!isOnline) {
      const result: SyncResult = {
        success: 0,
        failed: 0,
        total: 0,
        errors: ['Sem conexão com a internet']
      }
      this.notifyListeners('error', result)
      return result
    }

    // Sincronizar perguntas primeiro
    await this.syncQuestions()

    // Depois sincronizar leads
    return await this.syncPendingLeads()
  }

  /**
   * Auto-sync quando voltar online
   */
  static startAutoSync() {
    // Listener para mensagens do Service Worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', event => {
        if (event.data.type === 'SYNC_REQUEST') {
          console.log('SW solicitou sync:', event.data.tag)
          this.fullSync()
        }
      })
    }

    // Listener para quando voltar online
    window.addEventListener('online', async () => {
      console.log('Conexão restaurada, iniciando auto-sync...')
      toast('Conexão restaurada! Sincronizando dados...')
      
      // Aguardar um pouco para garantir que a conexão está estável
      setTimeout(async () => {
        await this.fullSync()
      }, 1000)
    })

    // Listener para quando ficar offline
    window.addEventListener('offline', () => {
      console.log('Conexão perdida')
      toast('Sem conexão. Dados serão salvos localmente.', { icon: '⚠️' })
      this.notifyListeners('idle')
    })

    // Sync periódico (a cada 30 segundos se online)
    setInterval(async () => {
      if (typeof navigator !== 'undefined' && navigator.onLine && !this.syncInProgress) {
        const pendingLeads = await OfflineService.getPendingLeads()
        if (pendingLeads.length > 0) {
          console.log('Auto-sync periódico iniciado')
          await this.syncPendingLeads()
        }
      }
    }, 30000) // 30 segundos
  }

  /**
   * Obter estatísticas de sync
   */
  static async getSyncStats() {
    const pendingLeads = await OfflineService.getPendingLeads()
    const hasOfflineData = await OfflineService.hasOfflineData()
    const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : false
    
    return {
      pendingLeads: pendingLeads.length,
      hasOfflineData,
      isOnline,
      canSync: isOnline && pendingLeads.length > 0
    }
  }
}