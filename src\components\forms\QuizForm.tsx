"use client"

import { Input } from '@/components/ui/Input'
import { RadioGroup } from '@/components/ui/RadioGroup'
import { useQuizForm } from '@/hooks/useQuizForm'
import { Controller } from 'react-hook-form'
import Button from '../ui/Button'

export function QuizForm() {
  const { 
    form, 
    perguntas, 
    loading, 
    submitting, 
    result, 
    isOnline,
    onSubmit,
    resetQuiz 
  } = useQuizForm({ offline: false })

  const { control, register, formState: { errors } } = form as any

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando perguntas...</p>
        </div>
      </div>
    )
  }

  if (result) {
    // Calcular faixa de faturamento baseada no faturamento médio
    const faturamentoMedio = (result.resultado.faturamento2025 + result.resultado.faturamento2026) / 2
    const faixaFaturamento = faturamentoMedio >= 1000000 
      ? `R$ ${(faturamentoMedio / 1000000).toFixed(1)}M` 
      : faturamentoMedio >= 1000 
        ? `R$ ${(faturamentoMedio / 1000).toFixed(0)}K` 
        : `R$ ${faturamentoMedio.toFixed(0)}`

    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            🎉 Sua Avaliação está Pronta!
          </h2>
          <p className="text-gray-600">
            Baseado nas suas respostas, calculamos o valor estimado da sua marca
          </p>
        </div>

        <div className="space-y-6">
          {/* Valor Principal */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white text-center">
            <h3 className="text-lg font-medium mb-2">Valor Estimado da Sua Marca</h3>
            <p className="text-4xl font-bold">{result.resultado.valorFormatado}</p>
            <p className="text-blue-100 mt-2">
              Baseado no faturamento de {faixaFaturamento}
            </p>
          </div>

          {/* Breakdown */}
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <h4 className="font-medium text-gray-900">Pontuação Total</h4>
              <p className="text-2xl font-bold text-blue-600">
                {result.resultado.pontuacaoTotal} pts
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <h4 className="font-medium text-gray-900">Fator de Valorização</h4>
              <p className="text-2xl font-bold text-green-600">
                {result.resultado.percentualFormatado}
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <h4 className="font-medium text-gray-900">Faturamento Base</h4>
              <p className="text-lg font-semibold text-gray-700">
                {faixaFaturamento}
              </p>
            </div>
          </div>

          {/* Explicação */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Como chegamos neste valor?</h4>
            <p className="text-blue-800 text-sm">
              Multiplicamos o faturamento médio mensal ({faixaFaturamento}) 
              pelo fator de valorização ({result.resultado.percentualFormatado}), que é baseado 
              na pontuação total de {result.resultado.pontuacaoTotal} pontos obtida nas suas respostas.
            </p>
          </div>

          {/* Ações */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button onClick={resetQuiz} variant="secondary" className="flex-1">
              Nova Avaliação
            </Button>
            <Button 
              onClick={() => window.print()} 
              variant="secondary" 
              className="flex-1"
            >
              Imprimir Resultado
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Quanto Vale Sua Marca?
          </h1>
          <p className="text-gray-600">
            Responda as perguntas abaixo para descobrir o valor estimado da sua marca
          </p>
          {!isOnline && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm">
                📱 Modo offline ativo - Dados serão sincronizados quando voltar online
              </p>
            </div>
          )}
        </div>

        <form onSubmit={(e) => { e.preventDefault(); onSubmit(form.getValues()); }} className="space-y-8">
          {/* Campos Fixos */}
          <div className="grid md:grid-cols-2 gap-6">
            <Input
              label="Nome da Marca *"
              placeholder="Ex: Minha Empresa LTDA"
              {...register('nomeMarca', { 
                required: 'Nome da marca é obrigatório' 
              })}
              error={(errors as any)?.nomeMarca?.message as string}
            />
            
            <Input
              label="Seu Nome *"
              placeholder="Ex: João Silva"
              {...register('nome', { 
                required: 'Nome é obrigatório' 
              })}
              error={(errors as any)?.nome?.message as string}
            />
            
            <Input
              label="WhatsApp *"
              placeholder="Ex: (11) 99999-9999"
              {...register('whatsapp', { 
                required: 'WhatsApp é obrigatório' 
              })}
              error={(errors as any)?.whatsapp?.message as string}
            />
            
            <Input
              label="E-mail *"
              type="email"
              placeholder="Ex: <EMAIL>"
              {...register('email', { 
                required: 'E-mail é obrigatório',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'E-mail inválido'
                }
              })}
              error={(errors as any)?.email?.message as string}
            />
          </div>

          {/* Perguntas Dinâmicas */}
          <div className="space-y-8">
            <h2 className="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Perguntas sobre sua marca
            </h2>
            
            {perguntas.map((pergunta, index) => (
              <div key={pergunta.id} className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {index + 1}. {pergunta.texto}
                  {pergunta.multiplicador && (
                    <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      Faturamento
                    </span>
                  )}
                </h3>
                
                <Controller
                  name={`respostas.${pergunta.id}`}
                  control={control}
                  rules={{ required: 'Esta pergunta é obrigatória' }}
                  render={({ field, fieldState }) => (
                    <RadioGroup
                      name={field.name}
                      value={field.value?.toString()}
                      onChange={field.onChange}
                      options={pergunta.opcoes.map(opcao => ({
                        value: opcao.id,
                        label: opcao.label
                      }))}
                      error={fieldState.error?.message}
                    />
                  )}
                />
              </div>
            ))}
          </div>

          {/* Botão Submit */}
          <div className="flex justify-center pt-6">
            <Button
              type="submit"
              disabled={submitting}
              size="lg"
              className="w-full sm:w-auto px-12"
            >
              {submitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Calculando...
                </>
              ) : (
                'Calcular Valor da Marca'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}