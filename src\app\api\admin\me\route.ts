import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

function verify(token: string, secret: string) {
  const parts = token.split('.')
  if (parts.length !== 3) return false
  const [encHeader, encPayload, encSig] = parts
  const data = `${encHeader}.${encPayload}`
  const expected = crypto.createHmac('sha256', secret).update(data).digest()
  const expectedB64Url = Buffer.from(expected)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
  return crypto.timingSafeEqual(Buffer.from(encSig), Buffer.from(expectedB64Url))
}

export async function GET(request: NextRequest) {
  const token = request.cookies.get('admin_auth')?.value
  if (!token) {
    return NextResponse.json({ authenticated: false }, { status: 200 })
  }
  const secret = process.env.ADMIN_JWT_SECRET || process.env.KIOSK_JWT_SECRET || ''
  const ok = secret ? verify(token, secret) : false
  return NextResponse.json({ authenticated: ok }, { status: 200 })
}


