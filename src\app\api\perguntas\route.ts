import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    const perguntas = await prisma.pergunta.findMany({
      include: {
        opcoes: {
          orderBy: { valor: 'asc' }
        }
      },
      orderBy: { ordem: 'asc' }
    })

    // Mapear para incluir um campo de config derivado (compat com schema atual)
    const perguntasComConfig = perguntas.map((p: any) => ({
      ...p,
      // Ordena opções em memória por ordemOpcao (se existir), senão por valor
      opcoes: [...(p.opcoes || [])].sort((a: any, b: any) => {
        const ao = (a as any)?.ordemOpcao
        const bo = (b as any)?.ordemOpcao
        if (ao == null && bo == null) return (a.valor ?? 0) - (b.valor ?? 0)
        if (ao == null) return 1
        if (bo == null) return -1
        return ao - bo
      }),
      config: p.config ?? inferPerguntaConfig(p)
    }))

    return NextResponse.json(perguntasComConfig)
  } catch (error) {
    console.error('Erro ao buscar perguntas:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

// Heurística para manter compatibilidade até migrarmos o schema no banco
function inferPerguntaConfig(p: any) {
  const cfg: Record<string, any> = {}
  if (p.tipo === 'NUMBER') {
    if (p.ordem === 7 || p.ordem === 8) cfg.numberFormat = 'currency'
    else if (p.ordem === 9) cfg.numberFormat = 'percentage'
    else if (p.ordem === 12) cfg.numberFormat = 'followers'
  }
  // Roles conhecidas para o cálculo
  if (p.ordem === 7) cfg.role = 'FATURAMENTO_2025'
  if (p.ordem === 8) cfg.role = 'FATURAMENTO_2026'
  if (p.ordem === 9) cfg.role = 'MARGEM_LUCRO'
  if (p.ordem === 12) cfg.role = 'SEGUIDORES'
  // Pontua por padrão para radios 10..16
  if (p.tipo === 'RADIO') cfg.pontua = true
  return cfg
}