declare module 'html-to-image' {
  export function toBlob(
    node: HTMLElement,
    options?: {
      cacheBust?: boolean;
      pixelRatio?: number;
      backgroundColor?: string;
      width?: number;
      height?: number;
      style?: Partial<CSSStyleDeclaration>;
      quality?: number;
      canvasWidth?: number;
      canvasHeight?: number;
      filter?: (node: HTMLElement) => boolean;
      skipFonts?: boolean;
      preferCssPageSize?: boolean;
    }
  ): Promise<Blob | null>;

  export function toJpeg(
    node: HTMLElement,
    options?: {
      cacheBust?: boolean;
      pixelRatio?: number;
      backgroundColor?: string;
      width?: number;
      height?: number;
      style?: Partial<CSSStyleDeclaration>;
      quality?: number; // 0..1
      canvasWidth?: number;
      canvasHeight?: number;
      filter?: (node: HTMLElement) => boolean;
      skipFonts?: boolean;
      preferCssPageSize?: boolean;
    }
  ): Promise<string>; // dataURL
}
