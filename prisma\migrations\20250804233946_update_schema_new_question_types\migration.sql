/*
  Warnings:

  - The `tipo` column on the `perguntas` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "public"."TipoPergunta" AS ENUM ('RADIO', 'CHECKBOX', 'NUMBER', 'TEXT', 'EMAIL', 'PHONE');

-- DropForeignKey
ALTER TABLE "public"."respostas_lead" DROP CONSTRAINT "respostas_lead_opcaoRespostaId_fkey";

-- AlterTable
ALTER TABLE "public"."leads" ALTER COLUMN "pontuacaoTotal" SET DATA TYPE DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "public"."opcoes_resposta" ALTER COLUMN "valor" SET DATA TYPE DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "public"."perguntas" ADD COLUMN     "obrigatoria" BOOLEAN NOT NULL DEFAULT true,
DROP COLUMN "tipo",
ADD COLUMN     "tipo" "public"."TipoPergunta" NOT NULL DEFAULT 'RADIO';

-- AlterTable
ALTER TABLE "public"."respostas_lead" ADD COLUMN     "valorNumerico" DOUBLE PRECISION,
ADD COLUMN     "valorTexto" TEXT,
ALTER COLUMN "opcaoRespostaId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."respostas_lead" ADD CONSTRAINT "respostas_lead_opcaoRespostaId_fkey" FOREIGN KEY ("opcaoRespostaId") REFERENCES "public"."opcoes_resposta"("id") ON DELETE SET NULL ON UPDATE CASCADE;
