{"name": "pwa-sua-marca-tem-valor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:generate": "prisma generate", "generate:icons": "node scripts/generate-icons.js", "generate:pwa-assets": "node scripts/generate-pwa-assets.js", "build:pwa": "npm run generate:pwa-assets && next build", "calc": "npx tsx scripts/calc-cli.ts"}, "prisma": {"seed": "npx tsx prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "clsx": "^2.1.1", "critters": "^0.0.23", "dexie": "^4.0.11", "googleapis": "^155.0.0", "html-to-image": "^1.11.13", "next": "15.4.5", "next-pwa": "^5.6.0", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "sharp": "^0.34.3", "svg2png-cli": "^1.1.1", "tailwindcss": "^4", "typescript": "^5"}}