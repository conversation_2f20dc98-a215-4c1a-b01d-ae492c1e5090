"use client"

import React from 'react'
import styles from './styles/OptionButton.module.css'

interface OptionButtonProps {
  children: React.ReactNode
  selected?: boolean
  onClick?: () => void
  disabled?: boolean
  className?: string
  stars?: number
  maxStars?: number
  multiple?: boolean
  clamp?: 2 | 3 | 4
}

export function OptionButton({
  children,
  selected = false,
  onClick,
  disabled = false,
  className,
  stars,
  maxStars = 5,
  multiple = false,
  clamp = 2,
}: OptionButtonProps) {
  const buttonClasses = [
    styles.button,
    selected && styles.buttonSelected,
    className
  ].filter(Boolean).join(' ')

  const contentClass = stars && stars > 0 ? styles.content : styles.contentSimple
  const clampClass = clamp === 4 ? styles.labelClamp4 : clamp === 3 ? styles.labelClamp3 : styles.labelClamp2

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
    >
      <div className={contentClass}>
        <span className={styles.text}>
          <span className={`${styles.labelText} ${clampClass} ${selected ? styles.labelTextSelected : ''}`}>{children}</span>
        </span>
        {typeof stars === 'number' && stars > 0 && (
          <span className={styles.starsFixed}>
            <span className={styles.stars} aria-hidden>
              {Array.from({ length: maxStars }).map((_, i) => (
                <svg key={i} width="14" height="14" viewBox="0 0 20 20" fill="currentColor" className={i < stars ? styles.starFilled : styles.starEmpty}>
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.802 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.802-2.034a1 1 0 00-1.176 0l-2.802 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </span>
          </span>
        )}
        
        <div className={styles.iconContainer}>
          {/* Unified selected icon: 29x29 circle with white check */}
          {selected && (
            <div className={styles.checkIcon}>
              <svg width="18" height="18" viewBox="0 0 20 20" fill="currentColor" aria-hidden>
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      </div>
    </button>
  )
}