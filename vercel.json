{"env": {"DATABASE_URL": "@database-url"}, "build": {"env": {"NEXT_PUBLIC_VERCEL_URL": "$VERCEL_URL"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/icons/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}