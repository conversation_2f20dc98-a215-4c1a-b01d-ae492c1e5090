"use client"

import { useState, useEffect } from 'react'
import { OfflineService } from '@/lib/indexeddb'
import { SyncManager } from '@/lib/sync-manager'
import toast from 'react-hot-toast'

export function useAdminData() {
  const [isOnline, setIsOnline] = useState(true)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle')

  useEffect(() => {
    // Verificar se estamos no cliente
    let updateOnlineStatus: (() => void) | undefined
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      // Verificar status online/offline
      updateOnlineStatus = () => {
        setIsOnline(navigator.onLine)
      }

      window.addEventListener('online', updateOnlineStatus)
      window.addEventListener('offline', updateOnlineStatus)
      updateOnlineStatus()
    }

    // Listener para status de sync
    const handleSyncStatus = (status: any) => {
      setSyncStatus(status)
    }

    SyncManager.addSyncListener(handleSyncStatus)

    return () => {
      if (updateOnlineStatus) {
        window.removeEventListener('online', updateOnlineStatus)
        window.removeEventListener('offline', updateOnlineStatus)
      }
      SyncManager.removeSyncListener(handleSyncStatus)
    }
  }, [])

  // Buscar dados com fallback offline
  const fetchWithFallback = async (url: string, options?: RequestInit) => {
    try {
      if (isOnline) {
        const response = await fetch(url, options)
        if (response.ok) {
          return await response.json()
        }
        throw new Error('Network request failed')
      } else {
        throw new Error('Offline mode')
      }
    } catch (error) {
      console.log('Tentando dados offline para:', url)
      
      // Fallback para dados offline baseado na URL
      if (url.includes('/api/perguntas')) {
        const offlinePerguntas = await OfflineService.getQuestionsOffline()
        if (offlinePerguntas.length > 0) {
          toast('Usando dados offline - Perguntas')
          return offlinePerguntas
        }
      }
      
      // Para outras URLs, retornar erro
      throw error
    }
  }

  // Sincronizar dados manualmente
  const syncData = async () => {
    if (!isOnline) {
      toast('Sem conexão para sincronizar', { icon: '⚠️' })
      return
    }

    try {
      setSyncStatus('syncing')
      const result = await SyncManager.fullSync()
      
      if (result.success > 0) {
        toast.success(`${result.success} itens sincronizados!`)
        setSyncStatus('success')
      } else if (result.failed > 0) {
        toast.error(`${result.failed} itens falharam na sincronização`)
        setSyncStatus('error')
      } else {
        toast('Nenhum dado pendente para sincronizar')
        setSyncStatus('idle')
      }
    } catch (error) {
      console.error('Erro na sincronização manual:', error)
      toast.error('Erro ao sincronizar dados')
      setSyncStatus('error')
    }
  }

  return {
    isOnline,
    syncStatus,
    fetchWithFallback,
    syncData
  }
}