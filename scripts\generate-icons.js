const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Tamanhos dos ícones PWA necessários
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];

// Diretórios
const INPUT_SVG = path.join(__dirname, '../public/logo.svg');
const OUTPUT_DIR = path.join(__dirname, '../public/icons');

// Garantir que o diretório existe
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

async function generateIcons() {
  console.log('🎨 Gerando ícones PWA...');
  
  try {
    // Ler o SVG
    const svgBuffer = fs.readFileSync(INPUT_SVG);
    
    // Gerar cada tamanho
    for (const size of ICON_SIZES) {
      const outputPath = path.join(OUTPUT_DIR, `icon-${size}x${size}.png`);
      
      await sharp(svgBuffer)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .png({
          quality: 100,
          compressionLevel: 9
        })
        .toFile(outputPath);
      
      console.log(`✅ Criado: icon-${size}x${size}.png`);
    }
    
    // Gerar favicon especial (quadrado com padding)
    const faviconPath = path.join(__dirname, '../public/favicon-pwa.png');
    await sharp(svgBuffer)
      .resize(512, 512, {
        fit: 'contain',
        background: { r: 38, g: 154, b: 182, alpha: 1 } // #269AB6
      })
      .png({
        quality: 100,
        compressionLevel: 9
      })
      .toFile(faviconPath);
    
    console.log('✅ Criado: favicon-pwa.png');
    
    // Gerar splash screens para iOS
    const splashSizes = [
      { width: 1125, height: 2436, name: 'iphone-x' },
      { width: 828, height: 1792, name: 'iphone-xr' },
      { width: 1242, height: 2688, name: 'iphone-xs-max' },
      { width: 750, height: 1334, name: 'iphone-8' },
      { width: 1242, height: 2208, name: 'iphone-8-plus' },
      { width: 1536, height: 2048, name: 'ipad' },
      { width: 2048, height: 2732, name: 'ipad-pro' }
    ];
    
    for (const splash of splashSizes) {
      const splashPath = path.join(OUTPUT_DIR, `splash-${splash.name}.png`);
      
      await sharp({
        create: {
          width: splash.width,
          height: splash.height,
          channels: 4,
          background: { r: 249, g: 250, b: 251, alpha: 1 } // #f9fafb
        }
      })
      .composite([
        {
          input: await sharp(svgBuffer)
            .resize(Math.floor(Math.min(splash.width * 0.4, splash.height * 0.4)), null, {
              fit: 'contain',
              withoutEnlargement: true
            })
            .toBuffer(),
          gravity: 'center'
        }
      ])
      .png({
        quality: 90,
        compressionLevel: 9
      })
      .toFile(splashPath);
      
      console.log(`✅ Criado: splash-${splash.name}.png`);
    }
    
    console.log('🎉 Todos os ícones PWA foram gerados com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao gerar ícones:', error);
    process.exit(1);
  }
}

// Executar
generateIcons();