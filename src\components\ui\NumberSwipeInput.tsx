'use client'

import React, { useMemo, useState, useEffect } from 'react'
import { FormattedInput } from './FormattedInput'
import styles from './styles/NumberSwipeInput.module.css'

type FormatType = 'currency' | 'percentage' | 'followers'

export interface NumberSwipeInputProps {
  value: number | ''
  onChange: (value: number) => void
  formatType: FormatType
  min?: number
  max?: number
  step?: number
  layout?: 'stack' | 'inline'
  showInput?: boolean
}

export function NumberSwipeInput({ value, onChange, formatType, min, max, step, layout = 'stack', showInput = true }: NumberSwipeInputProps) {
  const defaults = useMemo(() => {
    switch (formatType) {
      case 'currency':
        return { min: 0, max: 5_000_000, step: 1_000 }
      case 'percentage':
        return { min: 0, max: 100, step: 1 }
      case 'followers':
        return { min: 0, max: 10_000_000, step: 100 }
      default:
        return { min: 0, max: 100, step: 1 }
    }
  }, [formatType])

  const [internal, setInternal] = useState<number>(typeof value === 'number' ? value : 0)
  const [isEditing, setIsEditing] = useState(false)
  const [tempEdit, setTempEdit] = useState<string>('')

  useEffect(() => {
    if (!isEditing && typeof value === 'number') setInternal(value)
  }, [value, isEditing])

  const resolvedMin = min ?? defaults.min
  const resolvedMax = max ?? defaults.max
  const resolvedStep = step ?? defaults.step

  const handleSlider = (e: React.ChangeEvent<HTMLInputElement>) => {
    const next = Number(e.target.value)
    setInternal(next)
    onChange(next)
  }

  const handleInput = (next: number | string) => {
    if (typeof next === 'number') {
      const clamped = Math.max(resolvedMin, Math.min(resolvedMax, next))
      setInternal(clamped)
      onChange(clamped)
    }
  }

  const percent = ((internal - resolvedMin) / Math.max(1, resolvedMax - resolvedMin)) * 100

  if (!showInput) {
    return (
      <div className={styles.wrapper}>
        <div className={styles.valueWrapper}>
          <div
            className={styles.valueBubble}
            style={{ left: `${percent}%` }}
            role="button"
            tabIndex={0}
            onClick={() => {
              setTempEdit(String(internal))
              setIsEditing(true)
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setTempEdit(String(internal))
                setIsEditing(true)
              }
            }}
          >
            {isEditing ? (
              <input
                className={styles.valueInput}
                inputMode="numeric"
                type="number"
                min={resolvedMin}
                max={resolvedMax}
                step={resolvedStep}
                value={tempEdit}
                autoFocus
                onChange={(e) => {
                  const raw = e.target.value
                  setTempEdit(raw)
                  const parsed = Number(raw)
                  if (!Number.isNaN(parsed)) {
                    const clamped = Math.max(resolvedMin, Math.min(resolvedMax, Math.round(parsed)))
                    setInternal(clamped)
                    onChange(clamped)
                  }
                }}
                onBlur={() => {
                  const parsed = Number(tempEdit)
                  if (!Number.isNaN(parsed)) {
                    const clamped = Math.max(resolvedMin, Math.min(resolvedMax, Math.round(parsed)))
                    setInternal(clamped)
                    onChange(clamped)
                  }
                  setIsEditing(false)
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const parsed = Number(tempEdit)
                    if (!Number.isNaN(parsed)) {
                      const clamped = Math.max(resolvedMin, Math.min(resolvedMax, Math.round(parsed)))
                      setInternal(clamped)
                      onChange(clamped)
                    }
                    setIsEditing(false)
                  } else if (e.key === 'Escape') {
                    setIsEditing(false)
                  }
                }}
              />
            ) : (
              <span>{formatType === 'percentage' ? `${internal}%` : internal}</span>
            )}
          </div>
          <input
            type="range"
            className={styles.slider}
            min={resolvedMin}
            max={resolvedMax}
            step={resolvedStep}
            value={internal}
            onChange={handleSlider}
            aria-valuemin={resolvedMin}
            aria-valuemax={resolvedMax}
            aria-valuenow={internal}
          />
        </div>
        <div className={styles.scale}>
          <span>{formatType === 'percentage' ? '0%' : '0'}</span>
          <span>{formatType === 'percentage' ? '100%' : 'máx'}</span>
        </div>
      </div>
    )
  }

  if (layout === 'inline') {
    return (
      <div className={styles.wrapper}>
        <div className={styles.inlineRow}>
          <input
            type="range"
            className={styles.slider}
            min={resolvedMin}
            max={resolvedMax}
            step={resolvedStep}
            value={internal}
            onChange={handleSlider}
          />
          <div className={styles.inlineInput}>
            <FormattedInput
              placeholder={formatType === 'percentage' ? '0%' : '0'}
              formatType={formatType}
              value={typeof value === 'number' ? value : internal}
              onChange={handleInput}
            />
          </div>
        </div>
        <div className={styles.scale}>
          <span>{formatType === 'percentage' ? '0%' : '0'}</span>
          <span>{formatType === 'percentage' ? '100%' : 'máx'}</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.wrapper}>
      <div className={styles.inputRow}>
        <FormattedInput
          placeholder={formatType === 'currency' ? 'R$ 0,00' : formatType === 'percentage' ? '0%' : '0'}
          formatType={formatType}
          value={typeof value === 'number' ? value : internal}
          onChange={handleInput}
        />
      </div>
      <div className={styles.sliderRow}>
        <input
          type="range"
          className={styles.slider}
          min={resolvedMin}
          max={resolvedMax}
          step={resolvedStep}
          value={internal}
          onChange={handleSlider}
        />
        <div className={styles.scale}>
          <span>{formatType === 'percentage' ? '0%' : '0'}</span>
          <span>{formatType === 'percentage' ? '100%' : formatType === 'currency' ? 'máx' : 'máx'}</span>
        </div>
      </div>
    </div>
  )
}

export default NumberSwipeInput


