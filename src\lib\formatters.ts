// Formatadores de entrada para os campos do formulário

/**
 * Formatar número de telefone no padrão (XX) 99999-9999
 */
export function formatPhoneNumber(value: string): string {
  // Remove tudo que não é número
  const numbers = value.replace(/\D/g, '')
  
  // Limita a 11 dígitos
  const limitedNumbers = numbers.slice(0, 11)
  
  // Aplica a formatação
  if (limitedNumbers.length <= 2) {
    return limitedNumbers
  } else if (limitedNumbers.length <= 6) {
    return `(${limitedNumbers.slice(0, 2)}) ${limitedNumbers.slice(2)}`
  } else if (limitedNumbers.length <= 10) {
    return `(${limitedNumbers.slice(0, 2)}) ${limitedNumbers.slice(2, 6)}-${limitedNumbers.slice(6)}`
  } else {
    return `(${limitedNumbers.slice(0, 2)}) ${limitedNumbers.slice(2, 7)}-${limitedNumbers.slice(7)}`
  }
}

/**
 * Formatar valor monetário em Real brasileiro (sem centavos)
 */
export function formatCurrency(value: string | number): string {
  // Converte para string e remove caracteres não numéricos
  const stringValue = String(value).replace(/\D/g, '')
  
  if (!stringValue) return 'R$ 0'
  
  // Converte para número (em reais, não centavos)
  const numberValue = parseInt(stringValue, 10)
  
  // Formata como moeda brasileira sem centavos
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(numberValue)
}

/**
 * Converter valor formatado em moeda para número
 */
export function parseCurrencyToNumber(formattedValue: string): number {
  if (!formattedValue) return 0
  
  // Remove formatação e converte para número
  const cleanValue = formattedValue
    .replace(/[R$\s]/g, '')  // Remove R$, espaços
    .replace(/\./g, '')      // Remove pontos (separadores de milhares)
    .replace(',', '.')       // Converte vírgula decimal para ponto
  
  return parseFloat(cleanValue) || 0
}

/**
 * Formatar porcentagem
 */
export function formatPercentage(value: string | number): string {
  // Remove tudo que não é número ou ponto/vírgula
  const stringValue = String(value).replace(/[^\d,.]/, '')
  
  if (!stringValue) return '0%'
  
  // Converte vírgula para ponto
  const normalizedValue = stringValue.replace(',', '.')
  const numberValue = parseFloat(normalizedValue)
  
  if (isNaN(numberValue)) return '0%'
  
  // Limita a 100%
  const limitedValue = Math.min(numberValue, 100)
  
  return `${limitedValue}%`
}

/**
 * Converter porcentagem formatada para número
 */
export function parsePercentageToNumber(formattedValue: string): number {
  if (!formattedValue) return 0
  
  const cleanValue = formattedValue.replace('%', '').replace(',', '.')
  return parseFloat(cleanValue) || 0
}

/**
 * Validar se telefone está no formato correto
 */
export function isValidPhoneFormat(phone: string): boolean {
  const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/
  return phoneRegex.test(phone)
}

/**
 * Extrair apenas números do telefone
 */
export function extractPhoneNumbers(phone: string): string {
  return phone.replace(/\D/g, '')
}

/**
 * Formatar número de seguidores
 */
export function formatFollowersNumber(value: string | number): string {
  const stringValue = String(value).replace(/\D/g, '')
  
  if (!stringValue) return '0'
  
  const numberValue = parseInt(stringValue, 10)
  
  // Formatar com separadores de milhares
  return new Intl.NumberFormat('pt-BR').format(numberValue)
}