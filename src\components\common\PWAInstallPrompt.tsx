"use client"

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import styles from './styles/PWAInstallPrompt.module.css'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallButton, setShowInstallButton] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Verificar se estamos no browser
    if (typeof window === 'undefined') return
    
    // Verificar se já está instalado
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isInWebAppiOS = 'standalone' in (window.navigator as any) && (window.navigator as any).standalone
    
    if (isStandalone || isInWebAppiOS) {
      setIsInstalled(true)
      return
    }

         // Função para verificar se o PWA pode ser instalado
     const checkInstallability = () => {
       // Verificar se estamos no browser
       if (typeof window === 'undefined') return
       
              // Verificar se o navegador suporta PWA
       if (!('serviceWorker' in navigator)) {
         return
       }

       // Verificar se já tem um prompt armazenado
       const storedPrompt = localStorage.getItem('deferredPrompt')
       if (storedPrompt) {
         try {
           // Recriar o evento a partir do storage
           const event = new Event('beforeinstallprompt')
           window.dispatchEvent(event)
         } catch (error) {
           // Silenciar erro
         }
       }

       // Verificar se o PWA atende aos critérios básicos
       const hasManifest = document.querySelector('link[rel="manifest"]')
       const hasServiceWorker = 'serviceWorker' in navigator

       // Se atende aos critérios básicos, mostrar após um delay
       if (hasManifest && hasServiceWorker) {
         setTimeout(() => {
           setShowInstallButton(true)
           // Criar um prompt simulado
           const mockPrompt = {
             prompt: async () => {
               return Promise.resolve()
             },
             userChoice: Promise.resolve({ outcome: 'dismissed' as const })
           }
           setDeferredPrompt(mockPrompt as BeforeInstallPromptEvent)
         }, 3000) // 3 segundos de delay
       }
    }

         // Listener para o evento beforeinstallprompt
     const handleBeforeInstallPrompt = (e: Event) => {
       e.preventDefault()
       setDeferredPrompt(e as BeforeInstallPromptEvent)
       setShowInstallButton(true)
       
       // Armazenar o prompt para uso futuro
       localStorage.setItem('deferredPrompt', 'true')
     }

     // Listener para quando o app é instalado
     const handleAppInstalled = () => {
       setIsInstalled(true)
       setShowInstallButton(false)
       setDeferredPrompt(null)
       localStorage.removeItem('deferredPrompt')
     }

     // Verificar se o evento já foi disparado antes do listener ser adicionado
     if ('beforeinstallprompt' in window) {
       // Simular o evento se já estiver disponível
       setTimeout(() => {
         const event = new Event('beforeinstallprompt')
         window.dispatchEvent(event)
       }, 1000)
     }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    // Verificar instabilidade após um delay
    setTimeout(checkInstallability, 2000)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
             if (outcome === 'accepted') {
         // PWA instalado com sucesso
       } else {
         // Usuário rejeitou a instalação
       }
      
      setDeferredPrompt(null)
      setShowInstallButton(false)
         } catch (error) {
       // Silenciar erro de instalação
     }
  }



  // Não mostrar se já está instalado
  if (isInstalled) {
    return null
  }

  // Não mostrar se não há prompt disponível
  if (!showInstallButton || !deferredPrompt) {
    return null
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
                 <div className={styles.iconContainer}>
           <Image 
             src="/images/isotipo.png" 
             alt="MarcaValor"
             width={48}
             height={48}
             className={styles.icon}
             priority
           />
         </div>
        
        <div className={styles.textContainer}>
          <h3 className={styles.title}>
            Instalar MarcaValor
          </h3>
          <p className={styles.description}>
            Acesse rapidamente e funcione offline
          </p>
          
          <div className={styles.buttonContainer}>
            <button
              onClick={handleInstallClick}
              className={styles.installButton}
            >
              Instalar
            </button>
            
            <button
              onClick={() => setShowInstallButton(false)}
              className={styles.dismissButton}
            >
              Agora não
            </button>
          </div>
        </div>
        
        <button
          onClick={() => setShowInstallButton(false)}
          className={styles.closeButton}
        >
          <svg className={styles.closeIcon} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  )
}