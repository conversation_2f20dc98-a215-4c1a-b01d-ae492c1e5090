import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import crypto from 'crypto'

function verify(token: string, secret: string) {
  const parts = token.split('.')
  if (parts.length !== 3) return false
  const [encHeader, encPayload, encSig] = parts
  const data = `${encHeader}.${encPayload}`
  const expected = crypto.createHmac('sha256', secret).update(data).digest()
  const expectedB64Url = Buffer.from(expected)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
  return crypto.timingSafeEqual(Buffer.from(encSig), Buffer.from(expectedB64Url))
}

function isAdmin(req: NextRequest) {
  const token = req.cookies.get('admin_auth')?.value
  const secret = process.env.ADMIN_JWT_SECRET || process.env.KIOSK_JWT_SECRET || ''
  return Boolean(token && secret && verify(token, secret))
}

const protectedRoles = new Set(['FATURAMENTO_2025','FATURAMENTO_2026','MARGEM_LUCRO','SEGUIDORES'])
const protectedOrdensPrecisos = new Set([7,8,9,12])
const isProtectedPergunta = (p: any) => {
  const role = (p?.config as any)?.role
  return protectedOrdensPrecisos.has(p?.ordem) || (role && protectedRoles.has(role))
}

// Detalhe, atualizar e deletar pergunta (admin)
export async function GET(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }
    const { id } = await context.params
    const pergunta = await prisma.pergunta.findUnique({
      where: { id },
      include: { opcoes: { orderBy: { valor: 'asc' } } }
    })
    if (!pergunta) return NextResponse.json({ error: 'Não encontrada' }, { status: 404 })
    return NextResponse.json(pergunta)
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao carregar' }, { status: 500 })
  }
}

export async function PUT(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }
    const { id } = await context.params
    const body = await req.json()
    const { texto, tipo, multiplicador, ordem, config, opcoes, opcoesOrder } = body

    const atual = await prisma.pergunta.findUnique({ where: { id }, include: { opcoes: true } })
    if (!atual) return NextResponse.json({ error: 'Não encontrada' }, { status: 404 })

    const wasProtected = isProtectedPergunta(atual)
    const nextRole = config?.role ?? (atual as any)?.config?.role
    const nextOrdem = ordem ?? atual.ordem
    const willBeProtected = protectedOrdensPrecisos.has(nextOrdem) || (nextRole && protectedRoles.has(nextRole))

    // Não permitir tornar protegida via update e não permitir alterar campos críticos se já for protegida
    if (!wasProtected && willBeProtected) {
      return NextResponse.json({ error: 'Alteração inválida: não é permitido definir ordem/role críticos por aqui.' }, { status: 400 })
    }
    if (wasProtected) {
      if (ordem !== undefined && ordem !== atual.ordem) {
        return NextResponse.json({ error: 'Campo protegido: ordem não pode ser alterada.' }, { status: 400 })
      }
      if (tipo !== undefined && tipo !== atual.tipo) {
        return NextResponse.json({ error: 'Campo protegido: tipo não pode ser alterado.' }, { status: 400 })
      }
      const atualRole = (atual as any)?.config?.role
      if (config?.role !== undefined && config?.role !== atualRole) {
        return NextResponse.json({ error: 'Campo protegido: role não pode ser alterado.' }, { status: 400 })
      }
      if (Array.isArray(opcoes)) {
        return NextResponse.json({ error: 'Campo protegido: opções não podem ser alteradas.' }, { status: 400 })
      }
      if (Array.isArray(opcoesOrder)) {
        return NextResponse.json({ error: 'Campo protegido: ordem das opções não pode ser alterada.' }, { status: 400 })
      }
    }

    // Atualização básica do cabeçalho da pergunta
    let pergunta
    try {
      pergunta = await prisma.pergunta.update({
        where: { id },
        data: {
          ...(texto !== undefined ? { texto } : {}),
          ...(tipo !== undefined ? { tipo } : {}),
          ...(multiplicador !== undefined ? { multiplicador } : {}),
          ...(ordem !== undefined ? { ordem } : {}),
          // @ts-ignore
          ...(config !== undefined ? { config: config as any } : {})
        }
      })
    } catch {
      // Fallback se coluna config não existir
      pergunta = await prisma.pergunta.update({
        where: { id },
        data: {
          ...(texto !== undefined ? { texto } : {}),
          ...(tipo !== undefined ? { tipo } : {}),
          ...(multiplicador !== undefined ? { multiplicador } : {}),
          ...(ordem !== undefined ? { ordem } : {})
        }
      })
    }

    // Se opcoes vierem, substituí-las (simples)
    if (Array.isArray(opcoes)) {
      // apaga todas e recria
      await prisma.opcaoResposta.deleteMany({ where: { perguntaId: id } })
      await prisma.opcaoResposta.createMany({
        data: opcoes.map((o: any, idx: number) => ({ perguntaId: id, label: o.label, valor: o.valor, ordemOpcao: idx }))
      })
    }

    // Se somente uma nova ordem for enviada, aplicar nos registros existentes
    if (Array.isArray(opcoesOrder) && !Array.isArray(opcoes)) {
      // valida se todos os ids pertencem à pergunta
      const idsSet = new Set(atual.opcoes.map(o => o.id))
      for (const oid of opcoesOrder) {
        if (!idsSet.has(oid)) {
          return NextResponse.json({ error: 'IDs de opções inválidos para esta pergunta.' }, { status: 400 })
        }
      }
      // aplica ordem
      await Promise.all(opcoesOrder.map((oid: string, index: number) =>
        prisma.opcaoResposta.update({ where: { id: oid }, data: { ordemOpcao: index } })
      ))
    }

    const atualizado = await prisma.pergunta.findUnique({
      where: { id },
      include: { opcoes: { orderBy: { valor: 'asc' } } }
    })
    return NextResponse.json(atualizado)
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao atualizar' }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest, context: { params: Promise<{ id: string }> }) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }
    const { id } = await context.params
    const atual = await prisma.pergunta.findUnique({ where: { id } })
    if (!atual) return NextResponse.json({ error: 'Não encontrada' }, { status: 404 })
    if (isProtectedPergunta(atual)) {
      return NextResponse.json({ error: 'Pergunta crítica: exclusão bloqueada.' }, { status: 400 })
    }
    await prisma.opcaoResposta.deleteMany({ where: { perguntaId: id } })
    await prisma.pergunta.delete({ where: { id } })
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao deletar' }, { status: 500 })
  }
}
