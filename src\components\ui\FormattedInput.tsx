"use client"

import React, { forwardRef, useState, useEffect, useRef } from 'react'
import { 
  formatPhoneNumber, 
  formatCurrency, 
  formatPercentage,
  formatFollowersNumber,
  parseCurrencyToNumber,
  parsePercentageToNumber
} from '@/lib/formatters'
import styles from './styles/FormattedInput.module.css'

export interface FormattedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  label?: string
  error?: string
  formatType: 'phone' | 'currency' | 'percentage' | 'followers' | 'text'
  value?: string | number
  onChange?: (value: string | number) => void
}

export const FormattedInput = forwardRef<HTMLInputElement, FormattedInputProps>(
  ({ className, label, error, formatType, value, onChange, id, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState('')
    const [isFocused, setIsFocused] = useState(false)
    const localRef = useRef<HTMLInputElement | null>(null)

    // Merge forwarded ref with local ref
    const setRefs = (node: HTMLInputElement | null) => {
      localRef.current = node
      if (typeof ref === 'function') {
        ref(node)
      } else if (ref && 'current' in (ref as any)) {
        ;(ref as React.RefObject<HTMLInputElement | null>).current = node
      }
    }
    // Atualizar valor de exibição quando value prop muda
    useEffect(() => {
      // Se não há valor, deixar vazio para mostrar placeholder
      if (value === undefined || value === null || value === '') {
        setDisplayValue('')
        return
      }

      // Se há valor, formatar e exibir
      switch (formatType) {
        case 'phone':
          setDisplayValue(formatPhoneNumber(String(value)))
          break
        case 'currency':
          if (typeof value === 'number' && value > 0) {
            setDisplayValue(formatCurrency(value))
          } else if (typeof value === 'string' && value.trim() !== '') {
            setDisplayValue(formatCurrency(value))
          }
          break
        case 'percentage':
          if (typeof value === 'number' && value >= 0) {
            setDisplayValue(`${value}%`)
          } else if (typeof value === 'string' && value.trim() !== '') {
            setDisplayValue(formatPercentage(value))
          }
          break
        case 'followers':
          if (typeof value === 'number' && value >= 0) {
            setDisplayValue(formatFollowersNumber(value))
          } else if (typeof value === 'string' && value.trim() !== '') {
            setDisplayValue(formatFollowersNumber(value))
          }
          break
        default:
          setDisplayValue(String(value))
      }
    }, [value, formatType])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      
      // DEBUG: Log da mudança de input
      console.log(`🔍 DEBUG FormattedInput onChange:`, {
        formatType,
        inputValue,
        originalValue: value
      });
      
      switch (formatType) {
        case 'phone':
          const phoneFormatted = formatPhoneNumber(inputValue)
          setDisplayValue(phoneFormatted)
          onChange?.(phoneFormatted)
          break
        
        case 'currency':
          const currencyFormatted = formatCurrency(inputValue)
          const currencyNumber = parseCurrencyToNumber(currencyFormatted)
          
          // DEBUG: Log específico para currency
          console.log(`🔍 DEBUG Currency processing:`, {
            inputValue,
            currencyFormatted,
            currencyNumber,
            isNaN: isNaN(currencyNumber)
          });
          
          setDisplayValue(currencyFormatted)
          onChange?.(currencyNumber)
          break
        
        case 'percentage':
          const percentageFormatted = formatPercentage(inputValue)
          const percentageNumber = parsePercentageToNumber(percentageFormatted)
          
          // DEBUG: Log específico para percentage
          console.log(`🔍 DEBUG Percentage processing:`, {
            inputValue,
            percentageFormatted,
            percentageNumber,
            isNaN: isNaN(percentageNumber)
          });
          
          setDisplayValue(percentageFormatted)
          onChange?.(percentageNumber)
          break
        
        case 'followers':
          const followersFormatted = formatFollowersNumber(inputValue)
          const followersNumber = parseInt(inputValue.replace(/\D/g, ''), 10) || 0
          
          // DEBUG: Log específico para followers
          console.log(`🔍 DEBUG Followers processing:`, {
            inputValue,
            followersFormatted,
            followersNumber,
            isNaN: isNaN(followersNumber)
          });
          
          setDisplayValue(followersFormatted)
          onChange?.(followersNumber)
          break
        
        default:
          setDisplayValue(inputValue)
          onChange?.(inputValue)
      }
    }

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true)
      props.onFocus?.(e)
      // Give the browser a moment to resize for virtual keyboard, then scroll into view
      setTimeout(() => {
        try {
          localRef.current?.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'smooth' })
        } catch {}
      }, 250)
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false)
      props.onBlur?.(e)
    }

    const inputId = id || `formatted-input-${Math.random().toString(36).substr(2, 9)}`

    // Imperative autofocus to reliably open VKB on mobile and avoid sudden jumps
    useEffect(() => {
      if ((props as any).autoFocus && localRef.current) {
        // preventScroll avoids layout jump; we'll scroll smoothly after
        try { localRef.current.focus({ preventScroll: true } as any) } catch { localRef.current.focus() }
        // After focus, center smoothly to account for keyboard
        setTimeout(() => {
          try {
            localRef.current?.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'smooth' })
          } catch {}
        }, 300)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.autoFocus])

    const inputClasses = [
      styles.input,
      error && styles.inputError,
      isFocused && !error && styles.inputFocused,
      formatType === 'currency' && styles.currencyInput,
      formatType === 'percentage' && styles.percentageInput,
      formatType === 'phone' && styles.phoneInput,
      formatType === 'followers' && styles.followersInput,
      className
    ].filter(Boolean).join(' ')

    return (
      <div className={styles.container}>
        {label && (
          <label 
            htmlFor={inputId}
            className={styles.label}
          >
            {label}
          </label>
        )}
        
        <div className={styles.inputWrapper}>
          <input
            ref={setRefs}
            id={inputId}
            value={displayValue}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={inputClasses}
            {...props}
            type="text"
            // Hint keyboards according to formatType (set last to avoid being overridden)
            inputMode={
              formatType === 'phone' ? 'tel'
              : formatType === 'currency' ? 'decimal'
              : formatType === 'percentage' ? 'decimal'
              : formatType === 'followers' ? 'numeric'
              : undefined
            }
            pattern={
              formatType === 'phone' ? "[0-9()+\-\s]*"
              : formatType === 'currency' ? "[0-9.,]*"
              : formatType === 'percentage' ? "[0-9.,%]*"
              : formatType === 'followers' ? "[0-9]*"
              : undefined
            }
          />
        </div>
        
        {error && (
          <p className={styles.errorMessage}>
            <span className={styles.errorIcon}>⚠️</span>
            {error}
          </p>
        )}
      </div>
    )
  }
)

FormattedInput.displayName = "FormattedInput"