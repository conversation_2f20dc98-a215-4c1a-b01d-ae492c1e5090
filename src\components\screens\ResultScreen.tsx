'use client';

import React, { useCallback, useMemo, useRef, useState } from 'react';
import Image from 'next/image';
import styles from './styles/ResultScreen.module.css';
import { toBlob, toJpeg } from 'html-to-image';
import { usePathname } from 'next/navigation';

interface ResultScreenProps {
  result: {
    leadId: string;
    resultado: {
      valorEstimado: number;
      lucroTotal10Anos: number;
      pontuacaoTotal: number;
    };
  };
  nomeMarca: string;
  offline?: boolean;
  onNewConsultation?: () => void;
}

function formatValorMarcaBRL(valor: number): string {
  if (!Number.isFinite(valor)) return 'R$0';
  const inteiro = Math.round(valor);
  if (inteiro < 1_000_000) {
    return 'R$' + inteiro.toLocaleString('pt-BR');
  }
  // Trilhão(ões)
  if (inteiro >= 1_000_000_000_000) {
    const trilhoes = inteiro / 1_000_000_000_000;
    const arred = Math.round(trilhoes * 10) / 10; // 1 casa decimal
    const sufixo = arred >= 2 ? 'Trilhões' : 'Trilhão';
    const num = arred.toFixed(1);
    return `R$${num} ${sufixo}`;
  }
  // Bilhão(ões)
  if (inteiro >= 1_000_000_000) {
    const bilhoes = inteiro / 1_000_000_000;
    const arred = Math.round(bilhoes * 10) / 10; // 1 casa decimal
    const sufixo = arred >= 2 ? 'Bilhões' : 'Bilhão';
    const num = arred.toFixed(1);
    return `R$${num} ${sufixo}`;
  }
  // Milhão(ões)
  const milhoes = inteiro / 1_000_000;
  const arred = Math.round(milhoes * 10) / 10; // 1 casa decimal
  const sufixo = arred >= 2 ? 'Milhões' : 'Milhão';
  const num = arred.toFixed(1); // força ponto
  return `R$${num} ${sufixo}`;
}

const ResultScreen: React.FC<ResultScreenProps> = ({ result, nomeMarca, offline, onNewConsultation }) => {
  const captureRef = useRef<HTMLDivElement>(null);
  const [sharing, setSharing] = useState(false);
  const pathname = usePathname();
  const valor = result.resultado.valorEstimado ?? 0;
  const valorFormatado = useMemo(() => formatValorMarcaBRL(valor), [valor]);
  const lucro10Anos = result.resultado.lucroTotal10Anos ?? 0;
  const valuationTotalNegocio = lucro10Anos + valor; // soma lucro + valor da marca
  const valuationFormatado = useMemo(() => formatValorMarcaBRL(valuationTotalNegocio), [valuationTotalNegocio]);
  const pontuacao = result.resultado.pontuacaoTotal ?? 1;
  const pontuacaoMax = 33;
  // Arrays discretos conforme tabela (índice 0..32 para pontos 1..33)
  const labelSteps = [
    3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4, 5.6, 5.8,
    6.0, 6.2, 6.4, 6.6, 6.8, 7.0, 7.2, 7.4, 7.6, 7.8,
    8.0, 8.2, 8.4, 8.6, 8.8, 9.0, 9.2, 9.4, 9.6, 9.8, 10.0
  ];
  const idxRaw = Math.round(pontuacao) - 1; // garantir inteiro
  const idx = Math.max(0, Math.min(pontuacaoMax - 1, idxRaw));
  const labelValue = labelSteps[idx] ?? labelSteps[0];
  // Posição agora representa 0..10 ao longo de toda a barra (0%..100%)
  const markerPercentRaw = (labelValue / 10) * 100;
  const markerPercent = Math.max(0, Math.min(100, Math.round(markerPercentRaw * 10) / 10));
  const notaLabel = Number.isInteger(labelValue)
    ? labelValue.toFixed(0)
    : labelValue.toLocaleString('pt-BR', { minimumFractionDigits: 1, maximumFractionDigits: 1 });

  const handleShare = useCallback(async () => {
    if (sharing) return;
    try {
      setSharing(true);
      const node = captureRef.current;
      if (!node) return;

      // Aguarda fontes (evita troca de fonte/estouro de layout na captura)
      if (document && (document as any).fonts && (document as any).fonts.ready) {
        await (document as any).fonts.ready;
      }

      // Determina cor de fundo efetiva do nó para manter transparências corretas
      const getEffectiveBg = (el: HTMLElement | null): string => {
        let cur: HTMLElement | null = el;
        while (cur) {
          const bg = getComputedStyle(cur).backgroundColor;
          if (bg && bg !== 'rgba(0, 0, 0, 0)' && bg !== 'transparent') return bg;
          cur = cur.parentElement;
        }
        const bodyBg = getComputedStyle(document.body).backgroundColor;
        return bodyBg && bodyBg !== 'rgba(0, 0, 0, 0)' ? bodyBg : '#ffffff';
      };
      const backgroundColor = getEffectiveBg(node);

      // Aguarda imagens dentro do nó
      const imgs = Array.from(node.querySelectorAll('img')) as HTMLImageElement[];
      await Promise.all(
        imgs.map((img) =>
          img.complete && img.naturalWidth > 0
            ? Promise.resolve()
            : new Promise<void>((res) => {
                img.addEventListener('load', () => res(), { once: true });
                img.addEventListener('error', () => res(), { once: true });
              })
        )
      );

      // Gera imagem do conteúdo (sem a linha de compartilhar, pois está fora do ref)
      const rect = node.getBoundingClientRect();
      const ua = typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '';
      const isAndroid = ua.includes('android');
      const isMobile = /android|iphone|ipad|ipod/.test(ua);
      const desiredPixelRatio = isAndroid ? 1.25 : Math.min(2, window.devicePixelRatio || 1.5);
      const padPx = 40; // padding apenas na imagem gerada
      let blob = await toBlob(node, {
        cacheBust: true,
        pixelRatio: desiredPixelRatio,
        backgroundColor,
        skipFonts: false,
        width: Math.ceil(rect.width + padPx * 2),
        height: Math.ceil(rect.height + padPx * 2),
        style: {
          transform: 'none',
          '-webkit-transform': 'none' as any,
          padding: `${padPx}px`,
          boxSizing: 'border-box',
        } as any,
        filter: (el: HTMLElement) => {
          // Evita elementos marcados para não capturar
          if (el.closest?.(`.${styles.shareRow}`)) return false;
          if (el.getAttribute && el.getAttribute('data-no-capture') === 'true') return false;
          return true;
        },
      });
      if (!blob) throw new Error('Falha ao gerar imagem para compartilhamento');

      // Se o arquivo estiver grande em Android, tenta JPEG com qualidade menor
      const maxBytesForAndroid = 8 * 1024 * 1024; // 8MB guard
      const tryCompressJpeg = async (quality: number) => {
        const dataUrl = await toJpeg(node, {
          cacheBust: true,
          pixelRatio: desiredPixelRatio,
          backgroundColor,
          quality,
          width: Math.ceil(rect.width + padPx * 2),
          height: Math.ceil(rect.height + padPx * 2),
          style: { transform: 'none', '-webkit-transform': 'none', padding: `${padPx}px`, boxSizing: 'border-box' } as any,
          filter: (el: HTMLElement) => {
            if (el.closest?.(`.${styles.shareRow}`)) return false;
            if (el.getAttribute && el.getAttribute('data-no-capture') === 'true') return false;
            return true;
          },
        });
        // converte dataURL para Blob
        const res = await fetch(dataUrl);
        return await res.blob();
      };

      if (isAndroid && blob.size > maxBytesForAndroid) {
        try {
          blob = await tryCompressJpeg(0.9);
          if (blob.size > maxBytesForAndroid) blob = await tryCompressJpeg(0.8);
          if (blob.size > maxBytesForAndroid) blob = await tryCompressJpeg(0.7);
        } catch (e) {
          // mantém PNG original como fallback
        }
      }

      const file = new File([blob], blob.type === 'image/jpeg' ? 'resultado-marca.jpg' : 'resultado-marca.png', { type: blob.type || 'image/png' });
      const shareData: ShareData = {
        files: [file],
        title: 'Brand Valuation',
        text: `Resultado de ${nomeMarca}`,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
      } as any;

      // Em alguns Androids, arquivos muito grandes impedem abrir o sheet de compartilhamento
      const fileTooLarge = isAndroid && blob.size > maxBytesForAndroid;

      if (!fileTooLarge && navigator.canShare && navigator.canShare({ files: [file] as any })) {
        await navigator.share(shareData);
        return;
      }

      // Fallback garantido: tenta compartilhar apenas texto (abre o sheet nativo)
      if (navigator.share) {
        await navigator.share({ title: shareData.title, text: shareData.text });
        return;
      }

      // Fallback final: abre a imagem em nova aba
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
      // revoke depois de um tempo
      setTimeout(() => URL.revokeObjectURL(url), 30_000);
    } catch (err) {
      // Silencioso, ou poderíamos exibir um toast no futuro
      console.error(err);
    } finally {
      setSharing(false);
    }
  }, [nomeMarca, sharing]);

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        {/* Área que será convertida em imagem (exclui a seção de compartilhamento) */}
        <div ref={captureRef}>
        {/* Logo */}
        <div className={styles.logoContainer}>
          <Image
            src="/images/logotipo_branco.png"
            alt="Logo"
            width={229.25}
            height={42.96}
            priority
          />
        </div>

        {/* BRAND VALUATION (igual à InitialScreen) */}
        <div className={styles.brandValuationContainer}>
          <p className={styles.brandValuationText}>BRAND VALUATION</p>
        </div>

        {/* Stage de resultado (56px abaixo do BRAND VALUATION) */}
        <div className={styles.stage}>
          <h2 className={styles.stageTitle}>Valor estimado da marca</h2>
          <div className={styles.brandName}>{nomeMarca}</div>

          {/* Cartão do valor */}
          <div className={styles.valueCard}>
            <span className={styles.valuePrefix}>R$</span>
            <span className={styles.valueAmount}>
              {valorFormatado.replace(/^R\$/,'')}
            </span>
          </div>

          {/* Base de cálculo */}
          <div className={styles.baseCalcLabel}>Base de cálculo</div>
          {/* Valuation do negócio formatado (sem container) */}
          <div className={styles.businessValue}>
            {valuationFormatado}
          </div>
          <div className={styles.businessCaption}>Valuation total do negócio</div>

          {/* Escala com marcador */}
          <div className={styles.markerContainer}>
            <div className={styles.markerBaseWrapper}>
              <Image
                src="/images/base-marcador.png"
                alt="Base marcador"
                width={402}
                height={24}
                priority
              />
              <div
                className={styles.pinIcon}
                style={{ left: `calc(${markerPercent}% - 20px)` }}
                aria-label={`Pontuação ${pontuacao} de ${pontuacaoMax}`}
              >
                <span className={styles.pinLabel}>{notaLabel}</span>
              </div>
            </div>
          </div>

          {/* Reconhecimento de marca (8px abaixo da base) */}
          <div className={styles.recognitionLabel}>Reconhecimento de marca</div>
        </div>

        </div>
        {/* Compartilhe (60px abaixo do stage) */}
        <div
          className={styles.shareRow}
          role="button"
          tabIndex={0}
          aria-label="Compartilhar resultado"
          onClick={handleShare}
          onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') handleShare(); }}
        >
          <span className={styles.shareText}>Compartilhe</span>
          <Image
            className={styles.shareIcon}
            src="/icons/share-icon.png"
            alt="Compartilhar"
            width={46}
            height={46}
            priority
          />
        </div>
        {/* Nova Avaliação CTA (somente no kiosk) */}
        {pathname?.startsWith('/kiosk') && onNewConsultation && (
          <div className={styles.ctaContainer}>
            <button
              type="button"
              className={styles.ctaButton}
              onClick={onNewConsultation}
              aria-label="Iniciar nova avaliação"
            >
              <span className={styles.ctaLabel}>Nova Avaliação</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultScreen;