// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Pergunta {
  id           String         @id @default(cuid())
  texto        String
  tipo         TipoPergunta   @default(RADIO)
  multiplicador Boolean       @default(false)
  ordem        Int
  obrigatoria  Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  
  // Relações
  opcoes       OpcaoResposta[]
  respostas    RespostaLead[]

  @@map("perguntas")
}

model OpcaoResposta {
  id         String  @id @default(cuid())
  label      String
  valor      Float   // Mudando para Float para suportar decimais
  perguntaId String
  createdAt  DateTime @default(now())
  ordemOpcao Int?    // Ordem opcional para exibição (fallback para 'valor' quando nula)
  
  // Relações
  pergunta   Pergunta @relation(fields: [perguntaId], references: [id], onDelete: Cascade)
  respostas  RespostaLead[]

  @@map("opcoes_resposta")
}

model Lead {
  id              String   @id @default(cuid())
  clientId        String?  @unique
  nomeMarca       String
  nome            String
  whatsapp        String
  email           String
  pontuacaoTotal  Float    // Mudando para Float
  percentualFator Float
  valorEstimado   Float
  rdstationId     String?  // ID do lead no RD Station
  crmId           String?  // ID do lead no CRM
  sheetsRowId     String?
  syncedToSheetsAt DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relações
  respostas       RespostaLead[]

  @@map("leads")
}

model RespostaLead {
  id              String @id @default(cuid())
  leadId          String
  perguntaId      String
  opcaoRespostaId String?
  valorNumerico   Float?  // Para perguntas numéricas (seguidores, faturamento, etc.)
  valorTexto      String? // Para respostas de texto livre
  createdAt       DateTime @default(now())
  
  // Relações
  lead            Lead          @relation(fields: [leadId], references: [id], onDelete: Cascade)
  pergunta        Pergunta      @relation(fields: [perguntaId], references: [id])
  opcaoResposta   OpcaoResposta? @relation(fields: [opcaoRespostaId], references: [id])

  // Removendo a constraint unique para permitir múltiplas respostas
  @@map("respostas_lead")
}

// Enum para tipos de pergunta
enum TipoPergunta {
  RADIO      // Escolha única
  CHECKBOX   // Múltipla escolha
  NUMBER     // Campo numérico
  TEXT       // Campo de texto
  EMAIL      // Campo de email
  PHONE      // Campo de telefone
}
