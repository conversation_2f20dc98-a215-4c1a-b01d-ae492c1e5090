'use client';

import React, { useState } from 'react';
import InitialScreen from './screens/InitialScreen';
import { MultiStepQuizForm } from './forms/MultiStepQuizForm';
import { PWAInstallPrompt } from './common/PWAInstallPrompt';
import { PWAInitializer } from './common/PWAInitializer';
import { Toaster } from 'react-hot-toast';

type AppState = 'initial' | 'quiz';

const QuizApp: React.FC<{ offline?: boolean }> = ({ offline = true }) => {
  const [currentState, setCurrentState] = useState<AppState>('initial');

  const handleStartQuiz = () => {
    setCurrentState('quiz');
  };

  const handleBackToInitial = () => {
    setCurrentState('initial');
  };

  return (
    <>
      <PWAInitializer />
      <main className="min-h-svh text-white bg-[var(--primary-bg)]">
        {currentState === 'initial' && (
          <div className="center-grid min-h-svh px-4">
            <InitialScreen onStartQuiz={handleStartQuiz} />
          </div>
        )}

        {currentState === 'quiz' && (
          <div className='center-grid min-h-svh px-4'>
            <div className="w-full max-w-3xl">
              <MultiStepQuizForm offline={offline} />
            </div>
          </div>
        )}
        
        {offline && <PWAInstallPrompt />}
        
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1D3623',
              color: '#fff',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            },
          }}
        />
      </main>
    </>
  );
};

export default QuizApp;