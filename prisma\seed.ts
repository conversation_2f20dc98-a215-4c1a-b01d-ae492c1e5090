import { PrismaClient, TipoPergunta } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  console.log("🗑️ Limpando dados antigos...");
  await prisma.respostaLead.deleteMany();
  await prisma.opcaoResposta.deleteMany();
  await prisma.pergunta.deleteMany();

  console.log("✅ Dados antigos removidos. Inserindo novas perguntas...");

  // Lista de perguntas conforme Planilha do Chefe
  const perguntasData = [
    // Perguntas 1 a 4 (Lead) - Não precisam ser criadas no banco Pergunta

    // Pergunta 5
    {
      texto: "Qual das alternativas abaixo melhor define o seu negócio?",
      tipo: TipoPergunta.RADIO,
      ordem: 5,
      opcoes: [
        { label: "Vestuário", valor: 0 },
        { label: "Fabricação de produtos", valor: 0 },
        { label: "Comércio de produtos de outras marcas", valor: 0 },
        { label: "Prestação de serviços", valor: 0 },
        { label: "Software e/ou tecnologia", valor: 0 },
      ],
    },
    // Pergunta 6
    {
      texto: "Há quanto tempo sua marca está em utilização?",
      tipo: TipoPergunta.RADIO,
      ordem: 6,
      opcoes: [
        { label: "Menos de 1 ano", valor: 0 },
        { label: "1 a 3 anos", valor: 0 },
        { label: "3 a 5 anos", valor: 0 },
        { label: "Mais de 5 anos", valor: 0 },
      ],
    },
    // Pergunta 7
    {
      texto: "Qual a sua previsão de faturamento para 2025?",
      tipo: TipoPergunta.NUMBER,
      ordem: 7,
      opcoes: [],
    },
    // Pergunta 8
    {
      texto: "Qual a sua meta de faturamento para 2026?",
      tipo: TipoPergunta.NUMBER,
      ordem: 8,
      opcoes: [],
    },
    // Pergunta 9
    {
      texto: "Qual a sua margem de lucro estimada?",
      tipo: TipoPergunta.NUMBER,
      ordem: 9,
      opcoes: [],
    },
    // Pergunta 10
    {
      texto: "O quanto a sua marca é reconhecida no mercado?",
      tipo: TipoPergunta.RADIO,
      ordem: 10,
      opcoes: [
        { label: "Quase ninguém", valor: 1 },
        { label: "Algumas pessoas da minha rede", valor: 2 },
        { label: "Pessoas do meu segmento já reconhecem", valor: 3 },
        {
          label: "Bastante conhecida, sendo uma das principais do meu mercado",
          valor: 4,
        },
      ],
    },
    // Pergunta 11 - RADIO evolutiva (não CHECKBOX)
    {
      texto: "Sua marca está presente em quais canais digitais?",
      tipo: TipoPergunta.RADIO,
      ordem: 11,
      opcoes: [
        { label: "Apenas WhatsApp ou Instagram", valor: 1 },
        { label: "Redes sociais + Site institucional", valor: 2 },
        { label: "Redes + Site otimizado + Email Marketing", valor: 3 },
        { label: "Tudo acima + Mídia paga ativa", valor: 4 },
      ],
    },
    // Pergunta 12
    {
      texto:
        "Qual a quantidade total seguidores sua marca tem nas redes sociais?",
      tipo: TipoPergunta.NUMBER,
      ordem: 12,
      opcoes: [],
    },
    // Pergunta 13
    {
      texto: "Sua empresa possui uma base ativa de clientes?",
      tipo: TipoPergunta.RADIO,
      ordem: 13,
      opcoes: [
        { label: "Ainda estou conquistando os primeiros", valor: 1 },
        { label: "Já temos uma carteira razoável", valor: 2 },
        { label: "Temos clientes recorrentes e fidelizados", valor: 3 },
        { label: "Temos base ativa e evangelizadores da marca", valor: 4 },
      ],
    },
    // Pergunta 14
    {
      texto: "Qual foi o processo de desenvolvimento da sua marca?",
      tipo: TipoPergunta.RADIO,
      ordem: 14,
      opcoes: [
        { label: "Criei por conta própria ou com alguém da família", valor: 1 },
        { label: "Fiz um logo simples com designer", valor: 2 },
        {
          label: "Fiz com agência/designer especialista em branding",
          valor: 3,
        },
      ],
    },
    // Pergunta 15
    {
      texto: "Sua marca já está registrada no INPI?",
      tipo: TipoPergunta.RADIO,
      ordem: 15,
      opcoes: [
        { label: "Não", valor: 0 },
        { label: "Pedido em andamento", valor: 1 },
        { label: "Sim, já possuo o certificado de registro", valor: 4 },
      ],
    },
    // Pergunta 16
    {
      texto:
        "Quanto já foi investido em branding e marketing ao longo do tempo?",
      tipo: TipoPergunta.RADIO,
      ordem: 16,
      opcoes: [
        { label: "Menos de R$ 1.000", valor: 1 },
        { label: "R$ 1.000 a R$ 10.000", valor: 2 },
        { label: "R$ 10.000 a R$ 50.000", valor: 3 },
        { label: "R$ 50.000 a R$ 100.000", valor: 4 },
        { label: "Acima de R$ 100.000", valor: 5 },
      ],
    },
  ];

  // Inserção em cascata
  for (const pergunta of perguntasData) {
    const createdPergunta = await prisma.pergunta.create({
      data: {
        texto: pergunta.texto,
        tipo: pergunta.tipo,
        multiplicador: false,
        ordem: pergunta.ordem,
        obrigatoria: true,
        opcoes: {
          create: pergunta.opcoes.map((opt) => ({
            label: opt.label,
            valor: opt.valor,
          })),
        },
      },
    });
    console.log(`✅ Pergunta criada: ${createdPergunta.texto}`);
  }

  console.log("🎉 Seed finalizado com sucesso!");
}

main()
  .then(() => prisma.$disconnect())
  .catch((e) => {
    console.error(e);
    prisma.$disconnect();
    process.exit(1);
  });
