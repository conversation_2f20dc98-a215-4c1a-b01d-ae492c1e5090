-- CreateTable
CREATE TABLE "public"."perguntas" (
    "id" TEXT NOT NULL,
    "texto" TEXT NOT NULL,
    "tipo" TEXT NOT NULL DEFAULT 'radio',
    "multiplicador" BOOLEAN NOT NULL DEFAULT false,
    "ordem" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "perguntas_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."opcoes_resposta" (
    "id" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "valor" INTEGER NOT NULL,
    "perguntaId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "opcoes_resposta_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."leads" (
    "id" TEXT NOT NULL,
    "nomeMarca" TEXT NOT NULL,
    "nome" TEXT NOT NULL,
    "whatsapp" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "pontuacaoTotal" INTEGER NOT NULL,
    "percentualFator" DOUBLE PRECISION NOT NULL,
    "valorEstimado" DOUBLE PRECISION NOT NULL,
    "rdstationId" TEXT,
    "crmId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "leads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."respostas_lead" (
    "id" TEXT NOT NULL,
    "leadId" TEXT NOT NULL,
    "perguntaId" TEXT NOT NULL,
    "opcaoRespostaId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "respostas_lead_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "respostas_lead_leadId_perguntaId_key" ON "public"."respostas_lead"("leadId", "perguntaId");

-- AddForeignKey
ALTER TABLE "public"."opcoes_resposta" ADD CONSTRAINT "opcoes_resposta_perguntaId_fkey" FOREIGN KEY ("perguntaId") REFERENCES "public"."perguntas"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."respostas_lead" ADD CONSTRAINT "respostas_lead_leadId_fkey" FOREIGN KEY ("leadId") REFERENCES "public"."leads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."respostas_lead" ADD CONSTRAINT "respostas_lead_perguntaId_fkey" FOREIGN KEY ("perguntaId") REFERENCES "public"."perguntas"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."respostas_lead" ADD CONSTRAINT "respostas_lead_opcaoRespostaId_fkey" FOREIGN KEY ("opcaoRespostaId") REFERENCES "public"."opcoes_resposta"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
