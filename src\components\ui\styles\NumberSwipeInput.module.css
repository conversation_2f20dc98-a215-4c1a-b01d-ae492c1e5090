.wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  padding-inline: 0.5rem;
}

.inputRow {
  width: 100%;
}

.sliderRow {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.inlineRow {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 0.5rem;
}

.inlineInput {
  width: 100px;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.35);
  outline: none;
  touch-action: pan-y;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0,0,0,0.25);
  cursor: pointer;
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #fff;
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0,0,0,0.25);
  cursor: pointer;
}

.scale {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: rgba(255,255,255,0.75);
}

.valueWrapper {
  position: relative;
}

.valueBubble {
  position: absolute;
  top: -2.25rem;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  color: #111;
  padding: 0.25rem 0.5rem;
  font-size: 0.9rem;
  border-radius: 0.8rem;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2.25rem;
  min-height: 1.75rem;
  cursor: text;
}

.valueInput {
  width: 3.25rem;
  border: none;
  outline: none;
  background: transparent;
  text-align: center;
  font: inherit;
  color: #111;
}


