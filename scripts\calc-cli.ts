#!/usr/bin/env tsx
import readline from 'readline'
import { stdin as input, stdout as output } from 'node:process'
import { MarcaCalculator, RespostaCalculo } from '../src/lib/calculations'

function question(rl: readline.Interface, q: string): Promise<string> {
  return new Promise(resolve => rl.question(q, resolve))
}

function toNumber(value: string, fallback = 0): number {
  // suporta separador de milhar e decimal brasileiro
  const normalized = value.replace(/\./g, '').replace(/,/g, '.')
  const n = Number(normalized)
  return Number.isFinite(n) ? n : fallback
}

async function main() {
  const rl = readline.createInterface({ input, output })
  console.log('=== Calculadora de Valuation (CLI) ===')

  try {
    const ano1Str = await question(rl, 'Faturamento 2025 (ano1): ')
    const ano2Str = await question(rl, 'Faturamento 2026 (ano2): ')
    const margemStr = await question(rl, '<PERSON><PERSON><PERSON> <PERSON> lucro (%) ex: 25: ')
    const segStr = await question(rl, '<PERSON><PERSON><PERSON><PERSON> (ex: 12000): ')

    console.log('\nPontuação das perguntas (deixe em branco para 0)')
    const q10Str = await question(rl, 'Pergunta 10 (valor): ')
    const q11Str = await question(rl, 'Pergunta 11 (valor): ')
    const q13Str = await question(rl, 'Pergunta 13 (valor): ')
    const q14Str = await question(rl, 'Pergunta 14 (valor): ')
    const q15Str = await question(rl, 'Pergunta 15 (valor): ')
    const q16Str = await question(rl, 'Pergunta 16 (valor): ')

    const ano1 = toNumber(ano1Str)
    const ano2 = toNumber(ano2Str)
    const margem = toNumber(margemStr)
    const seguidores = toNumber(segStr)

    const q10 = toNumber(q10Str)
    const q11 = toNumber(q11Str)
    const q13 = toNumber(q13Str)
    const q14 = toNumber(q14Str)
    const q15 = toNumber(q15Str)
    const q16 = toNumber(q16Str)

    const respostas: RespostaCalculo[] = [
      { perguntaId: 'fat2025', role: 'FATURAMENTO_2025', valorNumerico: ano1, ordem: 7 },
      { perguntaId: 'fat2026', role: 'FATURAMENTO_2026', valorNumerico: ano2, ordem: 8 },
      { perguntaId: 'margem', role: 'MARGEM_LUCRO', valorNumerico: margem, ordem: 9 },
      { perguntaId: 'seguidores', role: 'SEGUIDORES', valorNumerico: seguidores, ordem: 12 },
      { perguntaId: 'q10', valor: q10, ordem: 10 },
      { perguntaId: 'q11', valor: q11, ordem: 11 },
      { perguntaId: 'q13', valor: q13, ordem: 13 },
      { perguntaId: 'q14', valor: q14, ordem: 14 },
      { perguntaId: 'q15', valor: q15, ordem: 15 },
      { perguntaId: 'q16', valor: q16, ordem: 16 },
    ]

    const resultado = MarcaCalculator.calcularValorMarca(respostas)

    console.log('\n--- Resultado ---')
    console.log(MarcaCalculator.obterResumoCalculo(resultado))

    console.log('\nProjeção 10 anos:')
    resultado.projecaoAnual.forEach((v, i) => {
      console.log(`Ano ${i + 1}: ${MarcaCalculator.formatarMoeda(v)}`)
    })

  } catch (err) {
    console.error('Erro ao executar CLI:', err)
  } finally {
    rl.close()
  }
}

main()
