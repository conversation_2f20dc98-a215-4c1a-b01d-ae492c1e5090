/* OptionButton Styles */

.button {
  width: 100%;
  border-radius: 60px;
  text-align: left;
  font-size: 20px;
  min-height: 56px;
  padding: 12px 22px 12px 22px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  background: white;
  color: #374151;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-family: 'Ralew<PERSON>', ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Desktop-only hover effects */
@media (hover: hover) and (pointer: fine) {
  .button:hover {
    background: #F9FAFB;
    box-shadow: 0 6px 12px -4px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    border-color: #E5E7EB;
  }
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.button:disabled:hover {
  transform: none;
}

/* Selected State */
.buttonSelected {
  border-color: #ffffff;
  border-width: 2px;
}

/* Content Layout */
.content {
  display: grid;
  grid-template-columns: 1fr minmax(40px, 60px) 32px; /* text | stars | check */
  align-items: center;
  column-gap: 0.65rem;
  width: 100%;
} 

.text {
  line-height: 1.4;
  min-width: 0;
}

.labelText {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  font-family: 'Raleway', ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Selected label text: black and extra-bold */
.labelTextSelected {
  color: #000000;
  font-weight: 800;
}

/* Line clamps and adaptive font sizes for long labels */
.labelClamp2 { -webkit-line-clamp: 2; line-clamp: 2; max-height: calc(1.4em * 2); }
.labelClamp3 { -webkit-line-clamp: 3; line-clamp: 3; max-height: calc(1.4em * 3); }
.labelClamp4 { -webkit-line-clamp: 4; line-clamp: 4; max-height: calc(1.4em * 4); }

/* Slightly shrink font for longer labels to improve density */
.labelText.labelClamp3 { font-size: 13px; }
.labelText.labelClamp4 { font-size: 15px; }

/* Stars inside text area */
.starsFixed {
  width: auto; /* do not stretch */
  min-width: 80px; /* reserve enough space for up to 5 stars */
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: center; /* center within grid cell */
}

/* Simple layout (sem pontuação): apenas texto + ícone fixo */
.contentSimple {
  display: grid;
  grid-template-columns: 1fr 29px; /* text | check */
  align-items: center;
  column-gap: 0.75rem;
  width: 100%;
}

.stars {
  display: inline-flex;
  gap: 0.15rem;
}

.starFilled {
  color: #10A4B2;
}

.starEmpty {
  color:#E1E1E1;
}

.iconContainer {
  margin-left: 0.25rem;
  flex-shrink: 0;
  display: grid;
  place-items: center;
  width: 29px; /* reserve space matching the check icon */
}

/* Check Icon: 29x29 dark circle with white check */
.checkIcon {
  width: 29px;
  height: 29px;
  background: #202F21;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.checkIcon svg {
  width: 18px;
  height: 18px;
  color: #ffffff;
}

/* Arrow Icon */
.arrowIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: #9CA3AF;
  transition: transform 0.2s ease;
}

.button:hover .arrowIcon {
  transform: translateX(4px);
}

/* Selection Animation */
.buttonSelected {
  animation: selectPulse 0.6s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8px 16px -4px rgba(16, 185, 129, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px -2px rgba(16, 185, 129, 0.3);
  }
}

/* Ripple Effect */
.button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(191, 246, 255, 0.178);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.button:active::before {
  width: 100%;
  height: 100%;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  .button {
    font-size: 20px;
  }
  
  .checkIcon {
    width: 29px;
    height: 29px;
  }
  
  .checkIcon svg {
    width: 18px;
    height: 18px;
  }
  
  .arrowIcon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* Very small screens: prioritize label text over stars */
@media (max-width: 375px) {
  .button {
    font-size: 18px; /* slightly smaller to avoid truncation */
  }

  .content {
    grid-template-columns: 1fr minmax(32px, 58px) 29px; /* shrink stars, keep check size */
    column-gap: 0.4rem;
  }

  .starsFixed {
    min-width: 56px; /* allow tighter star area */
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .button,
  .arrowIcon,
  .button::before {
    transition: none;
  }
  
  .buttonSelected {
    animation: none;
  }
  
  .button:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .button {
    border: 2px solid #374151;
  }
  
  .buttonSelected {
    border: 2px solid #ffffff;
  }
}

/* Focus visible for keyboard navigation */
.button:focus-visible {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}