.wrapper {
  display: grid;
  grid-template-rows: 1fr auto;
  gap: 0.75rem;
  width: 100%;
}

.viewport {
  position: relative;
  height: clamp(220px, 45vh, 360px);
  overflow: hidden;
}

.scroller {
  height: 100%;
  overflow-y: auto;
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.slide {
  width: 100%;
  border: 1px solid rgba(255,255,255,0.25);
  background: rgba(255,255,255,0.08);
  color: #fff;
  border-radius: 1rem;
  padding: 1rem;
  display: grid;
  grid-auto-flow: row;
  align-items: center;
  justify-items: center;
  gap: 0.5rem;
  scroll-snap-align: center;
  min-height: 112px;
  margin: 0.5rem 0;
  transition: transform 0.2s ease, background 0.2s ease, border-color 0.2s ease;
}

.slide:active {
  transform: scale(0.98);
}

.stars {
  display: flex;
  gap: 0.25rem;
}

.starFilled {
  color: #FFD166;
}

.starEmpty {
  color: rgba(255,255,255,0.25);
}

.label {
  font-weight: 700;
  text-align: center;
}

.activeHint {
  font-size: 0.75rem;
  color: rgba(255,255,255,0.7);
}

.controls {
  display: flex;
  justify-content: center;
}

.primary {
  background: #fff;
  color: #252525;
  border-radius: 0.8rem;
  font-weight: 600;
  min-width: 120px;
  height: 36px;
}


