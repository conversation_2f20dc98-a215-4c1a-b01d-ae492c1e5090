.container {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  /* Usa a cor primária com variação mais escura via color-mix */
  background: linear-gradient(
    135deg,
    color-mix(in oklab, var(--primary-bg), white 0%) 0%,
    color-mix(in oklab, var(--primary-bg), black 25%) 100%
  );
  border-radius: 1rem;
  box-shadow: 0 10px 25px color-mix(in oklab, var(--primary-bg), transparent 70%);
  padding: 1.25rem;
  z-index: 9999;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  max-width: 24rem;
  margin: 0 auto;
}

.content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.iconContainer {
  flex-shrink: 0;
  position: relative;
}

.icon {
  border-radius: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.textContainer {
  flex: 1;
  min-width: 0;
}

.title {
  font-family: var(--font-raleway);
  font-size: 1rem;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.description {
  font-family: var(--font-raleway);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.buttonContainer {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.installButton {
  background: #FFFFFF;
  color: var(--primary-bg);
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-family: var(--font-raleway);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.installButton:hover {
  background: #F8F9FA;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.installButton:active {
  transform: translateY(0);
}

.dismissButton {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: none;
  padding: 0.5rem 0.75rem;
  font-family: var(--font-raleway);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.375rem;
}

.dismissButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.closeButton {
  flex-shrink: 0;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.closeIcon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsividade */
@media (min-width: 768px) {
  .container {
    left: auto;
    right: 1rem;
    bottom: 1.5rem;
  }
  
  .title {
    font-size: 1.125rem;
  }
  
  .description {
    font-size: 0.9375rem;
  }
}

/* Animação de entrada */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.container {
  animation: slideUp 0.3s ease-out;
} 