.page {
  /* <PERSON><PERSON>a neutra (escopo local) */
  --surface: rgba(17, 24, 39, 0.85);
  --surface-2: rgba(31, 41, 55, 0.65);
  --border: rgba(148, 163, 184, 0.18);
  --muted: rgba(226, 232, 240, 0.9);
  --text: #F8FAFC;
  --primary: #269AB6;
  --primary-hover: #1e7a94;
  --danger: #e5484d;
  --danger-hover: #c23a3f;

  min-height: 100svh;
  min-height: 100dvh;
  width: 100%;
  /* Fundo neutro escuro para afastar do verde global */
  background: radial-gradient(1200px 600px at 20% -10%, rgba(38,154,182,0.15), transparent 60%),
              radial-gradient(1000px 500px at 90% 10%, rgba(30,122,148,0.2), transparent 55%),
              linear-gradient(180deg, #0B1220 0%, #0D1324 100%);
}

.container {
  max-width: 960px;
  margin: 0 auto;
  padding: 24px 16px 48px;
  color: var(--text);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.title {
  font-size: 22px;
  font-weight: 800;
  letter-spacing: 0.2px;
}

.actions {
  display: flex;
  gap: 8px;
}

.addButton {
  background: var(--primary);
  color: #fff;
  border: 1px solid transparent;
  border-radius: 10px;
  padding: 10px 14px;
  font-weight: 700;
  transition: background 120ms ease, transform 120ms ease;
}

.addButton:hover {
  background: var(--primary-hover);
}
.addButton:active {
  transform: translateY(1px);
}

.error {
  background: rgba(229, 72, 77, 0.14);
  border: 1px solid rgba(229, 72, 77, 0.35);
  color: #ffd5d6;
  padding: 10px 12px;
  border-radius: 10px;
  margin-bottom: 14px;
}

.list {
  display: grid;
  gap: 14px;
}

.card {
  position: relative;
  backdrop-filter: saturate(120%) blur(6px);
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 14px;
  padding: 16px;
}

.blockedOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.001);
  cursor: not-allowed;
  z-index: 50;
  pointer-events: all;
}

.row {
  display: grid;
  grid-template-columns: 1.7fr 0.9fr 0.6fr auto;
  gap: 12px;
  align-items: end;
}

@media (max-width: 860px) {
  .row {
    grid-template-columns: 1fr;
  }
}

.field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.label {
  font-size: 12px;
  color: var(--muted);
  font-weight: 600;
}

.input,
.select {
  height: 42px;
  width: 100%;
  border-radius: 10px;
  padding: 0 12px;
  color: var(--text);
  background: var(--surface-2);
  border: 1px solid var(--border);
  outline: none;
  transition: border-color 120ms ease, box-shadow 120ms ease;
}

.input:focus,
.select:focus {
  border-color: rgba(29, 185, 84, 0.6);
  box-shadow: 0 0 0 3px rgba(29, 185, 84, 0.18);
}

.checkboxWrap {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 4px;
}
.checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--primary);
}

.buttons {
  display: flex;
  gap: 8px;
  justify-content: end;
  margin-top: 12px;
}
.button {
  padding: 10px 12px;
  border-radius: 10px;
  border: 1px solid var(--border);
  background: linear-gradient(180deg, rgba(31,41,55,0.85) 0%, rgba(17,24,39,0.85) 100%);
  color: var(--text);
  font-weight: 700;
}
.save {
  background: var(--primary);
  border-color: transparent;
}
.save:hover {
  background: var(--primary-hover);
}
.delete {
  background: rgba(229, 72, 77, 0.2);
  border-color: rgba(229, 72, 77, 0.35);
}
.delete:hover {
  background: var(--danger);
  border-color: transparent;
}

.subGrid {
  margin-top: 14px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}
@media (max-width: 860px) {
  .subGrid {
    grid-template-columns: 1fr;
  }
}

.options {
  margin-top: 12px;
}

.optionsHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.sectionTitle {
  font-weight: 700;
  font-size: 14px;
  color: var(--muted);
}

.addOption {
  padding: 8px 10px;
  border-radius: 8px;
  background: var(--surface-2);
  border: 1px solid var(--border);
  color: var(--text);
  font-weight: 700;
}
.addOption:hover {
  background: rgba(255, 255, 255, 0.12);
}

.optionRow {
  display: grid;
  grid-template-columns: 28px 1fr 120px 40px; /* drag, label, valor, delete */
  gap: 8px;
  align-items: center;
}

.dragHandle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  color: var(--muted);
  cursor: grab;
  user-select: none;
  background: transparent;
}
.dragHandle:active {
  cursor: grabbing;
}
.optionInput {
  height: 42px;
  width: 100%;
  border-radius: 10px;
  padding: 0 12px;
  color: var(--text);
  background: var(--surface-2);
  border: 1px solid var(--border);
}
.optionNumber {
  height: 42px;
  width: 100%;
  border-radius: 10px;
  padding: 0 12px;
  color: var(--text);
  background: var(--surface-2);
  border: 1px solid var(--border);
  text-align: right;
}
.optionDelete {
  padding: 10px 12px;
  border-radius: 10px;
  border: 1px solid var(--border);
  background: rgba(229, 72, 77, 0.2);
  color: var(--text);
  font-weight: 700;
}
