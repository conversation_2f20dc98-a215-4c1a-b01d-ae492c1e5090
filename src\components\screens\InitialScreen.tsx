'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import styles from './styles/InitialScreen.module.css';

interface InitialScreenProps {
  onStartQuiz: () => void;
}

const InitialScreen: React.FC<InitialScreenProps> = ({ onStartQuiz }) => {
  useEffect(() => {
    // Previne scroll na tela inicial de forma mais segura
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    
    return () => {
      // Restaura o valor original
      document.body.style.overflow = originalOverflow || '';  
    };
  }, []);

  return (
      <div className={styles.container}>
        {/* Logo */}
        <div className={styles.logoContainer}>
          <Image
            src="/images/logotipo_branco.png"
            alt="Logo"
            width={229.25}
            height={42.96}
            priority
          />
        </div>
    
        {/* BRAND VALUATION */}
        <div className={styles.brandValuationContainer}>
          <p className={styles.brandValuationText}>BRAND VALUATION</p>
        </div>

        {/* Título */}
        <div className={styles.titleContainer}>
          <h1 className={styles.title}>QUANTO VALE SUA MARCA?</h1>
        </div>

        {/* Subtítulo (duas partes) */}
        <div className={styles.subtitleContainer}>
          <p className={styles.subtitle}>
            <span className={styles.subtitlePart1}>Descubra em menos de 2 minutos</span>
            <span className={styles.subtitlePart2}> e concorra a um Plaud Note AI</span>
          </p>
        </div>

        {/* Imagem do prêmio */}
        <div className={styles.prizeImageContainer}>
          <Image
            src="/images/plaud-ai-transparent.png"
            alt="Plaud AI"
            width={276}
            height={257}
            priority
          />
        </div>

        {/* Tagline (sem espaço entre a imagem e o texto) */}
        <div className={styles.taglineContainer}>
          <p className={styles.tagline}>O gravador com IA que vai maximizar sua produtividade.</p>
        </div>

        {/* Botão CTA "DESCOBRIR" com ícone */}
        <div className={styles.buttonContainer}>
          <button className={styles.ctaButton} onClick={onStartQuiz}>
            <span className={styles.ctaLabel}>DESCOBRIR</span>
              <Image
                src="/icons/arrow-right.svg"
                alt="Arrow Right"
                width={24}
                height={27}
                priority
              />
          </button>
        </div>
      </div>
  );
};

export default InitialScreen;