import { z } from 'zod'

// Schema para validação dos dados pessoais
export const dadosPessoaisSchema = z.object({
  nomeMarca: z.string()
    .min(2, 'Nome da marca deve ter pelo menos 2 caracteres')
    .max(100, 'Nome da marca deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ0-9\s&.-]+$/, 'Nome da marca contém caracteres inválidos'),
  
  nome: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras'),
  
  whatsapp: z.string()
    .min(14, 'WhatsApp deve ter o formato (XX) 99999-9999')
    .max(15, 'WhatsApp deve ter o formato (XX) 99999-9999')
    .regex(/^\(\d{2}\)\s\d{4,5}-\d{4}$/, 'WhatsApp deve ter o formato (XX) 99999-9999'),
  
  email: z.string()
    .email('E-mail inválido')
    .min(5, 'E-mail deve ter pelo menos 5 caracteres')
    .max(100, 'E-mail deve ter no máximo 100 caracteres')
    .transform(val => val.toLowerCase())
})

// Schema para validação dos valores monetários
export const valorMonetarioSchema = z.number()
  .min(0, 'Valor deve ser maior que zero')
  .max(999999999, 'Valor muito alto')

// Schema para validação de porcentagem
export const porcentagemSchema = z.number()
  .min(0, 'Porcentagem deve ser maior que 0%')
  .max(100, 'Porcentagem deve ser menor que 100%')

// Schema completo do formulário
export const formularioSchema = z.object({
  // Dados pessoais
  nomeMarca: dadosPessoaisSchema.shape.nomeMarca,
  nome: dadosPessoaisSchema.shape.nome,
  whatsapp: dadosPessoaisSchema.shape.whatsapp,
  email: dadosPessoaisSchema.shape.email,
  
  // Respostas do questionário
  respostas: z.record(z.string(), z.union([z.string(), z.number()]))
})

export type FormularioData = z.infer<typeof formularioSchema>
export type DadosPessoais = z.infer<typeof dadosPessoaisSchema>