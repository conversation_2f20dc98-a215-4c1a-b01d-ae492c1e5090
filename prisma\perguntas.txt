Campos e Opções de Resposta
1. Informações Cadastrais (sem pontuação)

Nome da Marca

Nome

WhatsApp

Email

Qual das alternativas abaixo melhor define o seu negócio?

Vestuário (Moda e Acessórios)

Fabricação própria de produtos

Comércio de produtos de terceiros

Prestação de serviços

Software e/ou tecnologia

Há quanto tempo sua marca está em utilização?

Menos de 1 ano

1 a 3 anos

3 a 5 anos

Mais de 5 anos

2. Indicadores Financeiros (valores abertos, sem pontuação)

Qual a sua previsão de faturamento para 2025? (Campo aberto em R$)

Qual a sua meta de faturamento para 2026? (Campo aberto em R$)

Qual a sua margem de lucro estimada? (Campo aberto em %)

3. Perguntas com Pontuação (Branding e Marketing)

O quanto a sua marca é reconhecida no mercado?

Quase ninguém → 1

Algumas pessoas da minha rede → 2

Pessoas do meu segmento já reconhecem → 3

Bastante conhecida, sendo uma das principais do meu mercado → 4

Sua marca está presente em quais canais digitais? (múltipla escolha, considerar pontuação máxima ou soma ponderada)

Apenas WhatsApp ou Instagram → 1

Redes sociais + Site institucional → 2

Redes + Site otimizado + Email Marketing → 3

Tudo acima + Mídia paga ativa → 4

Qual a quantidade total de seguidores sua marca tem nas redes sociais? (Instagram, TikTok, X, YouTube etc.)

Campo numérico aberto (ex.: 2800)

Pontuação = (número de seguidores ÷ 10.000) + 1

Sua empresa possui uma base ativa de clientes?

Ainda estou conquistando os primeiros → 1

Já temos uma carteira razoável → 2

Temos clientes recorrentes e fidelizados → 3

Temos base ativa e evangelizadores da marca → 4

Qual foi o processo de desenvolvimento da sua marca?

Criei por conta própria ou com alguém da família → 1

Fiz um logo simples com designer → 2

Fiz com agência/designer especialista em branding → 3

Sua marca já está registrada no INPI?

Não → 0

Pedido em andamento → 1

Sim, já possuo o certificado de registro → 4

Quanto já foi investido em branding e marketing ao longo do tempo? (Inclui identidade visual, campanhas, redes sociais, fachada, site, materiais gráficos, anúncios etc.)

Menos de R$ 1.000 → 1

R$ 1.000 a R$ 10.000 → 2

R$ 10.000 a R$ 50.000 → 3

R$ 50.000 a R$ 100.000 → 4

Acima de R$ 100.000 → 5



exemplo de perguntas com pontuação:
// O quanto a sua marca é reconhecida no mercado?
const pergunta3 = await prisma.pergunta.create({
  data: {
    texto: 'O quanto a sua marca é reconhecida no mercado?',
    tipo: 'radio',
    multiplicador: false,
    ordem: 3
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Quase ninguém', valor: 1, perguntaId: pergunta3.id },
    { label: 'Algumas pessoas da minha rede', valor: 2, perguntaId: pergunta3.id },
    { label: 'Pessoas do meu segmento já reconhecem', valor: 3, perguntaId: pergunta3.id },
    { label: 'Bastante conhecida, sendo uma das principais do meu mercado', valor: 4, perguntaId: pergunta3.id }
  ]
})

// Sua marca está presente em quais canais digitais? (múltipla escolha)
const pergunta4 = await prisma.pergunta.create({
  data: {
    texto: 'Sua marca está presente em quais canais digitais?',
    tipo: 'checkbox',
    multiplicador: true,
    ordem: 4
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Apenas WhatsApp ou Instagram', valor: 1, perguntaId: pergunta4.id },
    { label: 'Redes sociais + Site institucional', valor: 2, perguntaId: pergunta4.id },
    { label: 'Redes + Site otimizado + Email Marketing', valor: 3, perguntaId: pergunta4.id },
    { label: 'Tudo acima + Mídia paga ativa', valor: 4, perguntaId: pergunta4.id }
  ]
})

// Sua empresa possui uma base ativa de clientes?
const pergunta5 = await prisma.pergunta.create({
  data: {
    texto: 'Sua empresa possui uma base ativa de clientes?',
    tipo: 'radio',
    multiplicador: false,
    ordem: 5
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Ainda estou conquistando os primeiros', valor: 1, perguntaId: pergunta5.id },
    { label: 'Já temos uma carteira razoável', valor: 2, perguntaId: pergunta5.id },
    { label: 'Temos clientes recorrentes e fidelizados', valor: 3, perguntaId: pergunta5.id },
    { label: 'Temos base ativa e evangelizadores da marca', valor: 4, perguntaId: pergunta5.id }
  ]
})

// Qual foi o processo de desenvolvimento da sua marca?
const pergunta6 = await prisma.pergunta.create({
  data: {
    texto: 'Qual foi o processo de desenvolvimento da sua marca?',
    tipo: 'radio',
    multiplicador: false,
    ordem: 6
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Criei por conta própria ou com alguém da família', valor: 1, perguntaId: pergunta6.id },
    { label: 'Fiz um logo simples com designer', valor: 2, perguntaId: pergunta6.id },
    { label: 'Fiz com agência/designer especialista em branding', valor: 3, perguntaId: pergunta6.id }
  ]
})

// Sua marca já está registrada no INPI?
const pergunta7 = await prisma.pergunta.create({
  data: {
    texto: 'Sua marca já está registrada no INPI?',
    tipo: 'radio',
    multiplicador: false,
    ordem: 7
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Não', valor: 0, perguntaId: pergunta7.id },
    { label: 'Pedido em andamento', valor: 1, perguntaId: pergunta7.id },
    { label: 'Sim, já possuo o certificado de registro', valor: 4, perguntaId: pergunta7.id }
  ]
})

// Quanto já foi investido em branding e marketing ao longo do tempo?
const pergunta8 = await prisma.pergunta.create({
  data: {
    texto: 'Quanto já foi investido em branding e marketing ao longo do tempo?',
    tipo: 'radio',
    multiplicador: false,
    ordem: 8
  }
})

await prisma.opcaoResposta.createMany({
  data: [
    { label: 'Menos de R$ 1.000', valor: 1, perguntaId: pergunta8.id },
    { label: 'R$ 1.000 a R$ 10.000', valor: 2, perguntaId: pergunta8.id },
    { label: 'R$ 10.000 a R$ 50.000', valor: 3, perguntaId: pergunta8.id },
    { label: 'R$ 50.000 a R$ 100.000', valor: 4, perguntaId: pergunta8.id },
    { label: 'Acima de R$ 100.000', valor: 5, perguntaId: pergunta8.id }
  ]
})
