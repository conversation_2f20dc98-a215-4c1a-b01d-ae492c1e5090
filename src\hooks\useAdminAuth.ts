"use client"

import { useState, useEffect } from 'react'
import { AdminAuth, AdminSession } from '@/lib/auth'
import { useRouter } from 'next/navigation'

export function useAdminAuth() {
  const [session, setSession] = useState<AdminSession | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = () => {
    const currentSession = AdminAuth.getSession()
    setSession(currentSession)
    setLoading(false)
  }

  const login = (username: string, password: string) => {
    const success = AdminAuth.login(username, password)
    if (success) {
      checkAuth()
      return true
    }
    return false
  }

  const logout = () => {
    AdminAuth.logout()
    setSession(null)
    router.push('/admin/login')
  }

  const renewSession = () => {
    AdminAuth.renewSession()
    checkAuth()
  }

  return {
    session,
    loading,
    isAuthenticated: !!session?.isAuthenticated,
    login,
    logout,
    renewSession
  }
}