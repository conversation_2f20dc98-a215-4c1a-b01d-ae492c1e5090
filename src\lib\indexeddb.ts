import Dexie, { Table } from 'dexie'

// Tipos para IndexedDB
export interface ILeadOffline {
  id?: number
  clientId?: string
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  respostas: IRespostaOffline[]
  pontuacaoTotal: number
  percentualFator: number
  valorEstimado: number
  // Novos campos para nova lógica de cálculo
  faturamento2025?: number
  faturamento2026?: number
  margemLucro?: number
  lucroTotal10Anos?: number
  projecaoAnual?: number[]
  rdstationId?: string | null  // ID do lead no RD Station
  crmId?: string | null        // ID do lead no CRM
  status: 'pendente' | 'sincronizado'
  createdAt: Date
}

export interface IRespostaOffline {
  perguntaId: string
  opcaoRespostaId?: string    // Para radio/checkbox
  opcaoRespostaIds?: string[] // Para checkbox múltipla
  valorNumerico?: number      // Para perguntas NUMBER (7, 8, 9, 12)
  valorTexto?: string         // Para perguntas TEXT (futuro)
}

export interface IPerguntaOffline {
  id: string
  texto: string
  tipo: string
  multiplicador: boolean
  ordem: number
  opcoes: IOpcaoRespostaOffline[]
  // Guarda configurações extras para renderização e cálculo
  config?: Record<string, any>
}

export interface IOpcaoRespostaOffline {
  id: string
  label: string
  valor: number
}

export class MarcaValorDB extends Dexie {
  leads!: Table<ILeadOffline>
  perguntas!: Table<IPerguntaOffline>

  constructor() {
    super('MarcaValorDB')
    
    // Versão 1 - Schema original
    this.version(1).stores({
      leads: '++id, status, createdAt',
      perguntas: 'id, ordem'
    })
    
    // Versão 2 - Suporte a novos campos (nova lógica de cálculo)
    this.version(2).stores({
      leads: '++id, status, createdAt, pontuacaoTotal, valorEstimado',
      perguntas: 'id, ordem, tipo'
    }).upgrade(tx => {
      console.log('🔄 Migrando IndexedDB para versão 2...')
      // Migração automática - Dexie adiciona novos campos como undefined/null
      return tx.table('leads').toCollection().modify((lead: any) => {
        // Inicializar novos campos se não existirem
        if (lead.faturamento2025 === undefined) lead.faturamento2025 = null
        if (lead.faturamento2026 === undefined) lead.faturamento2026 = null
        if (lead.margemLucro === undefined) lead.margemLucro = null
        if (lead.lucroTotal10Anos === undefined) lead.lucroTotal10Anos = null
        if (lead.projecaoAnual === undefined) lead.projecaoAnual = null
      })
    })

    // Versão 3 - Adiciona campo config em perguntas
    this.version(3).stores({
      leads: '++id, status, createdAt, pontuacaoTotal, valorEstimado',
      perguntas: 'id, ordem, tipo'
    }).upgrade(async tx => {
      console.log('🔄 Migrando IndexedDB para versão 3 (config em perguntas)...')
      const table = tx.table('perguntas') as any
      await table.toCollection().modify((pergunta: any) => {
        if (pergunta && pergunta.tipo === 'NUMBER' && !pergunta.config) {
          pergunta.config = {}
          if (pergunta.ordem === 7 || pergunta.ordem === 8) pergunta.config.numberFormat = 'currency'
          else if (pergunta.ordem === 9) pergunta.config.numberFormat = 'percentage'
          else if (pergunta.ordem === 12) pergunta.config.numberFormat = 'followers'
        }
      })
    })
  }
}

export const db = new MarcaValorDB()

// Teste de IndexedDB
export class IndexedDBTester {
  static async testConnection() {
    try {
      console.log('🧪 Testando IndexedDB...')
      
      // Teste básico
      if (!window.indexedDB) {
        console.error('❌ IndexedDB não suportado')
        return false
      }
      
      // Teste de abertura do database
      await db.open()
      console.log('✅ Database aberto com sucesso')
      
      // Teste de escrita
      const testLead: ILeadOffline = {
        nomeMarca: 'Teste',
        nome: 'Teste',
        whatsapp: '123456789',
        email: '<EMAIL>',
        respostas: [],
        pontuacaoTotal: 10,
        percentualFator: 0.1,
        valorEstimado: 1000,
        status: 'pendente',
        createdAt: new Date()
      }
      
      const id = await db.leads.add(testLead)
      console.log('✅ Lead de teste salvo com ID:', id)
      
      // Teste de leitura
      const retrieved = await db.leads.get(id)
      console.log('✅ Lead de teste recuperado:', retrieved)
      
      // Limpar teste
      await db.leads.delete(id)
      console.log('✅ Lead de teste removido')
      
      return true
    } catch (error) {
      console.error('❌ Erro no teste de IndexedDB:', error)
      return false
    }
  }
}

// Funções helper para IndexedDB
export class OfflineService {
  
  // Salvar lead offline
  static async saveLeadOffline(lead: Omit<ILeadOffline, 'id' | 'status' | 'createdAt'>) {
    try {
      console.log('🔄 OfflineService.saveLeadOffline - Iniciando...')
      console.log('📊 Dados do lead:', lead)
      
      // Verificar se IndexedDB está disponível
      if (!window.indexedDB) {
        throw new Error('IndexedDB não suportado neste navegador')
      }

      // Validar dados obrigatórios
      if (!lead.nomeMarca || !lead.nome || !lead.email) {
        throw new Error('Dados obrigatórios do lead estão faltando')
      }

      if (!lead.respostas || lead.respostas.length === 0) {
        throw new Error('Respostas do questionário estão faltando')
      }
      
      const leadOffline: ILeadOffline = {
        ...lead,
        status: 'pendente',
        createdAt: new Date()
      }
      
      console.log('💾 Objeto final para salvar:', leadOffline)
      
      // Garantir que o database está aberto
      await db.open()
      
      const id = await db.leads.add(leadOffline)
      console.log('✅ Lead salvo offline com ID:', id)
      
      // Verificar se realmente foi salvo
      const savedLead = await db.leads.get(id)
      console.log('🔍 Lead recuperado:', savedLead)
      
      // Contar total de leads
      const totalLeads = await db.leads.count()
      console.log('📊 Total de leads na base:', totalLeads)
      
      return id
    } catch (error: any) {
      console.error('❌ ERRO ao salvar lead offline:', error)
      console.error('Stack trace:', error.stack)
      throw error
    }
  }

  // Buscar leads pendentes
  static async getPendingLeads(): Promise<ILeadOffline[]> {
    try {
      return await db.leads.where('status').equals('pendente').toArray()
    } catch (error) {
      console.error('Erro ao buscar leads pendentes:', error)
      return []
    }
  }

  // Buscar leads sincronizados
  static async getSyncedLeads(): Promise<ILeadOffline[]> {
    try {
      return await db.leads.where('status').equals('sincronizado').reverse().sortBy('createdAt')
    } catch (error) {
      console.error('Erro ao buscar leads sincronizados:', error)
      return []
    }
  }

  // Marcar lead como sincronizado
  static async markLeadAsSynced(id: number) {
    try {
      await db.leads.update(id, { status: 'sincronizado' })
    } catch (error) {
      console.error('Erro ao marcar lead como sincronizado:', error)
    }
  }

  // Salvar perguntas no IndexedDB
  static async saveQuestions(perguntas: IPerguntaOffline[]) {
    try {
      await db.perguntas.clear()
      await db.perguntas.bulkAdd(perguntas)
      console.log('Perguntas salvas offline:', perguntas.length)
    } catch (error) {
      console.error('Erro ao salvar perguntas offline:', error)
      throw error
    }
  }

  // Buscar perguntas offline
  static async getQuestionsOffline(): Promise<IPerguntaOffline[]> {
    try {
      return await db.perguntas.orderBy('ordem').toArray()
    } catch (error) {
      console.error('Erro ao buscar perguntas offline:', error)
      return []
    }
  }

  // Verificar se há dados offline
  static async hasOfflineData(): Promise<boolean> {
    try {
      const questionsCount = await db.perguntas.count()
      return questionsCount > 0
    } catch (error) {
      console.error('Erro ao verificar dados offline:', error)
      return false
    }
  }
}