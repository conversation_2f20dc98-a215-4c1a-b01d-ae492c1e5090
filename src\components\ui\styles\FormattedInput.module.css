/* FormattedInput Styles */

.container {
  width: 100%;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.inputWrapper {
  position: relative;
}

.input {
  display: flex;
  height: 3rem;
  width: 100%;
  border-radius: 9999px;
  border: 1px solid #E5E7EB;
  background: white;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #111827;
  transition: all 0.2s ease;
}

.input::placeholder {
  color: #9CA3AF;
  opacity: 1;
}

.input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Error State */
.inputError {
  border-color: #f7e200;
}

.inputError:focus {
  border-color: #f7e200;
  box-shadow: 0 0 0 3px rgba(247, 226, 0, 0.1);
}

/* Focus State */
.inputFocused {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}


/* Error Message */
.errorMessage {
  font-size: 0.875rem;
  color: #f7e200;
  font-family: var(--font-raleway);
  padding-inline: 0.5rem;
  font-weight: bold;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
}

.errorIcon {
  margin-right: 0.25rem;
}

/* Input Types Specific Styles */
.currencyInput {
  text-align: center;
  font-weight: 500;
  font-size: 36px;
  line-height: 42px;
  color: #000000;

}

.percentageInput {
  text-align: right;
}

.followersInput {
  text-align: right;
}

/* Responsive */
@media (max-width: 640px) {
  .input {
    height: 3.5rem;
    font-size: 1.125rem;
  }

  .inputIcon {
    font-size: 1rem;
  }
}

/* Animations */
@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

.inputError {
  animation: shake 0.3s ease-in-out;
}

/* Focus animations */
.input:focus {
  transform: scale(1.02);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.errorMessage {
  animation: fadeIn 0.3s ease-out;
}