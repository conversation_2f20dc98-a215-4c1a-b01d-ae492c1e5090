import { google } from "googleapis";
import dotenv from "dotenv";

dotenv.config();

// Permitir uso de GOOGLE_APPLICATION_CREDENTIALS_SHEETS (preferencial)
// com fallback para GOOGLE_APPLICATION_CREDENTIALS
const credentialsPath =
  process.env.GOOGLE_APPLICATION_CREDENTIALS_SHEETS ||
  process.env.GOOGLE_APPLICATION_CREDENTIALS;

const auth = new google.auth.GoogleAuth({
  keyFile: credentialsPath,
  scopes: ["https://www.googleapis.com/auth/spreadsheets"],
});

export const sheets = google.sheets({ version: "v4", auth });
