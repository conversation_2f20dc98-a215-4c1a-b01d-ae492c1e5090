/* MultiStepQuizForm Styles */

.container {
  background: var(--primary-bg);
  /* Usa svh/dvh para evitar deslocamentos pela barra de endereço */
  min-height: 100svh;
  min-height: 100dvh;
  display: grid;
  /* Centraliza o wrapper no eixo X e Y independentemente da barra de endereço */
  place-items: center;
  padding-top: clamp(1rem, 4vh, 2rem);
  padding-inline: 0.5rem;
  padding-bottom: clamp(1rem, 3vh, 2rem);
  width: 100%;
}

.wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 1rem;
  width: 100%;
  max-width: 480px; /* Mobile-first */
  margin: 0 auto; /* Centraliza o wrapper */
}
/* Transition Loading */
.transitionOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  /* Remove blur and heavy effects for smoother transitions */
  background: transparent;
}
/* Content Area */
.content {
  width: 100%;
  background: transparent;
}

.header {
  text-align: center;
  margin-bottom: clamp(1.25rem, 5vh, 2.25rem);
}

.title {
  font-size: clamp(1.5rem, 4vw, 1.875rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .wrapper {
    padding: 0 2rem;
    max-width: 640px; /* Aumenta no tablet/desktop pequeno */
  }

  .title {
    font-size: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .wrapper {
    max-width: 720px; /* Mais largo em telas grandes */
  }
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.125rem;
}

/* Step Content */
.stepContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.inputContainer {
  width: 100%;
}

.optionsContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.multiHint {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  text-align: center;
  margin-top: -0.5rem;
  font-style: italic;
}

.multiHintMargem {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  text-align: center;
  margin-top: 0.5rem;
  margin-bottom: 3rem;
  font-style: italic;
}

.errorContainer {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(254, 242, 242, 1);
  border: 1px solid rgba(254, 202, 202, 1);
  border-radius: 0.5rem;
}

.errorText {
  color: #dc2626;
  text-align: center;
  font-size: 0.875rem;
}

/* Navigation */
.navigation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 29px;
  padding-inline: 0.5rem;
  margin-top: 3rem;
}

.primaryButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: #202F21;
  color: #ffffff;
  width: 134px;
  height: 57px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 26px;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: none;
}

.primaryButton:hover {
  transform: scale(1.05);
}

.primaryButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.backButton {
  background: transparent;
  border: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.placeholderButton {
  width: 60px;
  height: 30px;
  visibility: hidden;
}

/* OK text inside primary button */
.okText {
  font-weight: bold;
  font-size: 2rem;
  line-height: 1;
  color: #ffffff;
}

/* Responsive sizing below 500px: scale proportionally to base 134x57 */
@media (max-width: 500px) {
  .primaryButton {
    width: 120px;
    height: 52px;
    gap: calc(10px * (100vw / 500));
    font-size: calc(26px * (100vw / 500));
  }
  .okIcon {
    width: calc(26px * (100vw / 500));
    height: calc(29px * (100vw / 500));
  }
}

/* Loading Screen Styles */
.loadingContainer {
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
}
/* Result Screen Styles */
.resultContainer {
  min-height: 100svh;
  min-height: 100dvh;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.resultCard {
  max-width: 42rem;
  width: 100%;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
}

.resultHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.resultIcon {
  width: 5rem;
  height: 5rem;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.resultTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.resultDescription {
  color: #6b7280;
}

.resultContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.resultValue {
  background: linear-gradient(90deg, #269ab6 0%, #1e7a94 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  color: white;
  text-align: center;
}

.resultValueTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.resultValueAmount {
  font-size: 2.25rem;
  font-weight: 700;
}

.resultValueSubtitle {
  color: rgba(219, 234, 254, 1);
  margin-top: 0.5rem;
}

.resultGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.resultGridItem {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 0.75rem;
  text-align: center;
}

.resultGridTitle {
  font-weight: 500;
  color: #111827;
}

.resultGridValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: #269ab6;
}

.resultSummary {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
}

.resultSummaryTitle {
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  text-align: center;
}

.resultSummaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  font-size: 0.875rem;
}

.resultActions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-top: 1rem;
}

@media (min-width: 640px) {
  .resultActions {
    flex-direction: row;
  }
}

.resultButton {
  flex: 1;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resultButtonSecondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.resultButtonSecondary:hover {
  background: #e5e7eb;
}

.resultButtonPrimary {
  background: #269ab6;
  color: white;
  border: none;
}

.resultButtonPrimary:hover {
  background: #1e7a94;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.optionsContainer > * {
  animation: slideIn 0.3s ease-out;
  animation-fill-mode: both;
}

.optionsContainer > *:nth-child(1) {
  animation-delay: 0.1s;
}
.optionsContainer > *:nth-child(2) {
  animation-delay: 0.2s;
}
.optionsContainer > *:nth-child(3) {
  animation-delay: 0.3s;
}
.optionsContainer > *:nth-child(4) {
  animation-delay: 0.4s;
}
.optionsContainer > *:nth-child(5) {
  animation-delay: 0.5s;
}
.optionsContainer > *:nth-child(6) {
  animation-delay: 0.6s;
}
