"use client"

// Sistema de autenticação simples para admin
// Em produção, substitua por NextAuth.js ou similar

const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123' // Em produção, use hash + salt
}

export interface AdminSession {
  isAuthenticated: boolean
  username: string
  loginTime: number
}

export class AdminAuth {
  private static SESSION_KEY = 'admin_session'
  private static SESSION_DURATION = 8 * 60 * 60 * 1000 // 8 horas

  /**
   * Fazer login do admin
   */
  static login(username: string, password: string): boolean {
    try {
      if (username === ADMIN_CREDENTIALS.username && 
          password === ADMIN_CREDENTIALS.password) {
        
        const session: AdminSession = {
          isAuthenticated: true,
          username,
          loginTime: Date.now()
        }

        // Salvar sessão no localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem(this.SESSION_KEY, JSON.stringify(session))
        }

        return true
      }
      return false
    } catch (error) {
      console.error('Erro no login:', error)
      return false
    }
  }

  /**
   * Verificar se admin está autenticado
   */
  static isAuthenticated(): boolean {
    try {
      if (typeof window === 'undefined') return false

      const sessionData = localStorage.getItem(this.SESSION_KEY)
      if (!sessionData) return false

      const session: AdminSession = JSON.parse(sessionData)
      
      // Verificar se sessão expirou
      const now = Date.now()
      if (now - session.loginTime > this.SESSION_DURATION) {
        this.logout()
        return false
      }

      return session.isAuthenticated
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error)
      return false
    }
  }

  /**
   * Obter dados da sessão
   */
  static getSession(): AdminSession | null {
    try {
      if (typeof window === 'undefined') return null

      const sessionData = localStorage.getItem(this.SESSION_KEY)
      if (!sessionData) return null

      const session: AdminSession = JSON.parse(sessionData)
      
      // Verificar se sessão expirou
      if (!this.isAuthenticated()) {
        return null
      }

      return session
    } catch (error) {
      console.error('Erro ao obter sessão:', error)
      return null
    }
  }

  /**
   * Fazer logout
   */
  static logout(): void {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(this.SESSION_KEY)
      }
    } catch (error) {
      console.error('Erro no logout:', error)
    }
  }

  /**
   * Renovar sessão
   */
  static renewSession(): void {
    const session = this.getSession()
    if (session) {
      session.loginTime = Date.now()
      if (typeof window !== 'undefined') {
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(session))
      }
    }
  }
}