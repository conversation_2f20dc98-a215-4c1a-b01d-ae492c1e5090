.container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-inline: clamp(0.75rem, 4vw, 1.25rem);
  padding-top: calc(env(safe-area-inset-top) + clamp(24px, 6vh, 48px));
  padding-bottom: calc(env(safe-area-inset-bottom) + clamp(16px, 4vh, 32px));
  min-height: 100svh;
  box-sizing: border-box;
  overflow: hidden;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 780px;
}

/* Área capturada para compartilhamento (adiciona padding/margem interna) */
.captureArea {
  padding: clamp(16px, 4vw, 40px);
  box-sizing: border-box;
}

/* Logo Container */
.logoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 1.5rem;
}

/* BRAND VALUATION (como InitialScreen) */
.brandValuationContainer {
  width: 100%;
  display: flex;
  justify-content: center;
}

.brandValuationText {
  font-family: var(--font-raleway);
  text-transform: uppercase;
  letter-spacing: .8rem;
  font-size: clamp(0.75rem, 2vw, 1.5rem);
  font-weight: bold;
}

/* Stage de resultado */
.stage {
  margin-top: clamp(28px, 7.5vh, 56px);
  width: 100%;
  max-width: 436px; /* largura alvo */
  min-height: 553px; /* altura alvo */
  background: rgba(217, 217, 217, 0.14);
  border: 2px solid rgba(255, 255, 255, 0.39);
  border-radius: 27px;
  padding: 38px 16px 53px 16px; /* topo, laterais, bottom */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.stageTitle {
  font-family: var(--font-raleway);
  color: #FFFFFF;
  font-weight: 800; /* extrabold */
  font-size: clamp(22px, 4.5vw, 28px);
  margin: 0;
  text-align: center;
}

.brandName {
  font-family: var(--font-raleway);
  color: #000000;
  font-weight: 800; /* extrabold */
  font-size: clamp(22px, 4.5vw, 28px);
  text-align: center;
}

/* Cartão do valor */
.valueCard {
  margin-top: 8px;
  width: 100%;
  max-width: 402px;
  height: 114px;
  background: #202F21;
  border-radius: 27px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 0 16px;
  box-sizing: border-box;
}

.valuePrefix {
  font-family: var(--font-raleway);
  color: #FFFFFF;
  font-weight: 800;
  font-size: clamp(20px, 4.5vw, 28px);
  align-self: center; /* alinhado verticalmente ao centro */
}

.valueAmount {
  font-family: var(--font-raleway);
  font-style: normal;
  font-weight: 700;
  font-size: clamp(28px, 6vw, 50px);
  line-height: 47px; /* 94% */
  text-align: center;
  color: #FFFFFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* evita corte visual em telas pequenas */
}

/* Base de cálculo (24px abaixo do valueCard) */
.baseCalcLabel {
  margin-top: 24px;
  font-family: var(--font-raleway);
  font-style: italic;
  font-weight: 500;
  font-size: 16px;
  line-height: 36px; /* 225% */
  text-align: center;
  color: #FFFFFF;
}

/* Valor do faturamento total (24px abaixo do Base de cálculo) */
.businessValue {
  margin-top: 24px;
  font-family: var(--font-raleway);
  font-style: normal;
  font-weight: 800;
  font-size: clamp(24px, 4.5vw, 36px);
  line-height: 36px; /* 100% */
  text-align: center;
  color: #FFFFFF;
}

/* Legenda (4px abaixo do valor) */
.businessCaption {
  margin-top: 4px;
  font-family: var(--font-raleway);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 36px; /* 225% */
  text-align: center;
  color: #FFFFFF;
}

/* Marcador (48px abaixo) */
.markerContainer {
  margin-top: 48px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.markerBaseWrapper {
  position: relative;
  width: 100%;
  max-width: 402px;
  height: 24px; /* altura da imagem base */
}
.markerBaseWrapper :global(img) {
  width: 100% !important;
  height: auto !important;
  display: block;
}

/* Fallback do pin (substituir por background-image quando o SVG existir) */
.pinIcon {
  position: absolute; /* posicionado sobre a base */
  top: -50%;
  transform: translate(-50%, -50%);
  width: 56px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('/icons/pin-icon.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  pointer-events: none;
}

.pinLabel {
  font-family: var(--font-raleway);
  font-weight: bold;
  font-size: clamp(16px, 4.5vw, 24px);
  line-height: 1;
  color: #FFFFFF;
  margin-top: -1rem;
}

/* Reconhecimento de marca (8px abaixo da base) */
.recognitionLabel {
  margin-top: 8px;
  font-family: var(--font-raleway);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 36px; /* 225% */
  text-align: center;
  color: #FFFFFF;
}

/* Compartilhe (60px abaixo do stage) */
.shareRow {
  margin-top: 60px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  user-select: none;
}
.shareText {
  font-family: var(--font-raleway);
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px; /* identical to box height */
  color: #202F21;
}
.shareIcon {
  margin-left: 10px;
  display: inline-block;
}

/* CTA: Nova Avaliação */
.ctaContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 28px;
}

.ctaButton {
  width: 314px;
  height: 80px;
  background: #202F21;
  color: #FFFFFF;
  border: none;
  border-radius: 45px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
}

.ctaLabel {
  font-family: var(--font-raleway);
  font-weight: bold;
  letter-spacing: 0.06em;
  font-size: clamp(24px, 2.8vw, 30px);
}

/* Responsividade para telas muito pequenas */
@media (max-width: 390px) {
  /* Igual ao InitialScreen: reduzir apenas o font-size */
  .brandValuationText { font-size: 0.85rem; letter-spacing: 0.5rem; }
  /* Igual ao InitialScreen: ajustar tamanho do logo em telas muito pequenas */
  .logoContainer :global(img) {
    width: 180px !important;
    height: auto !important;
  }
  .stage { padding: 28px 12px 40px; gap: 16px; }
  .valueCard { height: 100px; border-radius: 22px; }
}

/* Alturas pequenas (~<=740px): compactar paddings mantendo safe-area */
@media (max-height: 740px) {
  .container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: calc(env(safe-area-inset-top) + 2rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
  }
  .stage {
    min-height: auto;
    padding: 28px 12px 32px;
    gap: 14px;
  }
  .markerContainer { margin-top: 28px; }
  .shareRow { margin-top: 40px; }
}

/* Alturas muito pequenas (<=600px): compressão máxima */
@media (max-height: 600px) {
  .container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: calc(env(safe-area-inset-top) + 0.5rem);
    padding-bottom: calc(env(safe-area-inset-bottom) + 0.5rem);
  }
  .stage {
    padding: 20px 10px 24px;
    gap: 12px;
  }
  .stageTitle { font-size: clamp(20px, 4.2vw, 24px); }
  .brandName { font-size: clamp(18px, 4vw, 24px); }
  .valueCard { height: 92px; }
  .valueAmount { font-size: clamp(22px, 6.5vw, 30px); }
  .businessValue { font-size: 28px; line-height: 32px; }
  .markerContainer { margin-top: 22px; }
  .recognitionLabel { margin-top: 6px; }
  .shareRow { margin-top: 28px; }
}

/* Breakpoint combinado (largura muito pequena ou altura menor que ~857px) */
@media (max-width: 390px), (max-height: 857px) {
  .container {
    padding-top: calc(env(safe-area-inset-top) + 48px);
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
  }
}

/* Impressão básica */
@media print {
  .container { background: white !important; }
  .brandValuationText { color: #0f172a; }
  .stage { border-color: rgba(0,0,0,0.2); }
}