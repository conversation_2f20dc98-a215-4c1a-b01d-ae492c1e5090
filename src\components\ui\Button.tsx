import React from 'react';
import { ButtonHTMLAttributes, forwardRef } from 'react';
import Image from 'next/image';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: string; // Caminho para o ícone
  iconPosition?: 'left' | 'right';
  iconWidth?: number;
  iconHeight?: number;
  children: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  icon,
  iconPosition = 'left',
  className = '',
  disabled,
  iconWidth,
  iconHeight,
  children,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center gap-1 font-raleway font-bold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50';
  
  const variantClasses = {
    primary: 'bg-[#1D3623] text-white hover:bg-[#152a1b] active:bg-[#0f1f15]',
    secondary: 'bg-white text-[#1D3623] hover:bg-gray-100 active:bg-gray-200'
  };
  
  const sizeClasses = {
    sm: 'px-4 py-2 text-sm rounded-full',
    md: 'px-6 py-3 text-base rounded-full',
    lg: 'px-8 py-4 text-lg rounded-full'
  };
  
  const widthClass = fullWidth ? 'w-full' : '';
  
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';
  
  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${widthClass}
    ${disabledClasses}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const renderIcon = () => {
    if (!icon) return null;
    return (
      <Image
        src={icon}
        alt=""
        width={iconWidth || 32}
        height={iconHeight || 32}
        className="flex-shrink-0"
        priority
      />
    );
  };

  return (
    <button
      ref={ref}
      className={buttonClasses}
      disabled={disabled}
      {...props}
    >
      {icon && iconPosition === 'left' && renderIcon()}
      <span>{children}</span>
      {icon && iconPosition === 'right' && renderIcon()}
    </button>
  );
});

Button.displayName = 'Button';

export { Button };
export default Button;