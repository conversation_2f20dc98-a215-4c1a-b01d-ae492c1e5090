/* <PERSON><PERSON><PERSON> gerenciadas por next/font (layout.tsx). Removidos @import remotos. */

@import "tailwindcss";

:root {
  --primary-bg: #5BB551;
  --primary-text: #FFFFFF;
  --button-bg: #1D3623;
  /* --font-raleway é injetada por next/font como variável CSS em layout.tsx */
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-raleway), 'Raleway', system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, 'Helvetica Neue', Arial, 'Noto Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height quando suportado */
  position: relative;
}

body {
  color: var(--primary-text);
  background-color: var(--primary-bg);
  /* Prevenção de bounce scroll no iOS */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}
/* Utilitários personalizados */
/* Scrollbar customizada para o tema verde */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Helpers de viewport para evitar salto com barra de endereço mobile */
.min-h-svh {
  /* svh: small viewport height (considera UI visível) */
  min-height: 100svh;
  /* fallback para navegadores que só suportam dvh */
  min-height: 100dvh;
}

.h-svh {
  height: 100svh;
  height: 100dvh;
}

.center-grid {
  display: grid;
  place-items: center;
}