import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import crypto from 'crypto'

function verify(token: string, secret: string) {
  const parts = token.split('.')
  if (parts.length !== 3) return false
  const [encHeader, encPayload, encSig] = parts
  const data = `${encHeader}.${encPayload}`
  const expected = crypto.createHmac('sha256', secret).update(data).digest()
  const expectedB64Url = Buffer.from(expected)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
  return crypto.timingSafeEqual(Buffer.from(encSig), Buffer.from(expectedB64Url))
}

function isAdmin(req: NextRequest) {
  const token = req.cookies.get('admin_auth')?.value
  const secret = process.env.ADMIN_JWT_SECRET || process.env.KIOSK_JWT_SECRET || ''
  return Boolean(token && secret && verify(token, secret))
}

const protectedRoles = new Set(['FATURAMENTO_2025','FATURAMENTO_2026','MARGEM_LUCRO','SEGUIDORES'])
const protectedOrdensPrecisos = new Set([7,8,9,12])

// Lista e cria perguntas (admin)
export async function GET(req: NextRequest) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }
    const perguntas = await prisma.pergunta.findMany({
      include: { opcoes: { orderBy: { valor: 'asc' } } },
      orderBy: { ordem: 'asc' }
    })
    const ordenadas = perguntas.map((p: any) => ({
      ...p,
      opcoes: [...(p.opcoes || [])].sort((a: any, b: any) => {
        const ao = (a as any)?.ordemOpcao
        const bo = (b as any)?.ordemOpcao
        if (ao == null && bo == null) return (a.valor ?? 0) - (b.valor ?? 0)
        if (ao == null) return 1
        if (bo == null) return -1
        return ao - bo
      })
    }))
    return NextResponse.json(ordenadas)
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao listar perguntas' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 })
    }
    const body = await req.json()
    const { texto, tipo, multiplicador = false, ordem, config, opcoes = [] } = body
    if (protectedOrdensPrecisos.has(ordem) || (config?.role && protectedRoles.has(config.role))) {
      return NextResponse.json({ error: 'Criação bloqueada: ordem/role críticos não podem ser definidos.' }, { status: 400 })
    }
    let pergunta
    try {
      pergunta = await prisma.pergunta.create({
        data: {
          texto,
          tipo,
          multiplicador,
          ordem,
          // @ts-ignore: caso o schema ainda não tenha a coluna jsonb
          config: config as any,
          opcoes: { create: opcoes.map((o: any, idx: number) => ({ label: o.label, valor: o.valor, ordemOpcao: idx })) }
        },
        include: { opcoes: true }
      })
    } catch (err) {
      // Fallback: sem coluna config
      pergunta = await prisma.pergunta.create({
        data: {
          texto,
          tipo,
          multiplicador,
          ordem,
          opcoes: { create: opcoes.map((o: any, idx: number) => ({ label: o.label, valor: o.valor, ordemOpcao: idx })) }
        },
        include: { opcoes: true }
      })
    }
    return NextResponse.json(pergunta, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: 'Erro ao criar pergunta' }, { status: 500 })
  }
}
