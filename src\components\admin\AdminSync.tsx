"use client"

import { useAdminData } from '@/hooks/useAdminData'
import { Button } from '@/components/ui/Button'

export function AdminSync() {
  const { isOnline, syncStatus, syncData } = useAdminData()

  if (isOnline && syncStatus === 'idle') {
    return null // Não mostrar quando online e sem problemas
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white rounded-lg shadow-lg border p-4 min-w-[250px]">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {!isOnline ? (
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
            ) : syncStatus === 'syncing' ? (
              <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse" />
            ) : (
              <div className="w-3 h-3 bg-green-500 rounded-full" />
            )}
            
            <span className="font-medium text-sm text-gray-900">
              {!isOnline ? 'Admin Offline' : 
               syncStatus === 'syncing' ? 'Sincronizando...' :
               syncStatus === 'error' ? 'Erro na Sync' : 'Admin Online'}
            </span>
          </div>
        </div>

        <div className="space-y-3">
          {!isOnline && (
            <div className="text-sm text-gray-600">
              <p>🔧 Modo admin offline ativo</p>
              <p>Algumas funcionalidades podem estar limitadas</p>
            </div>
          )}

          {syncStatus === 'syncing' && (
            <div className="flex items-center space-x-2 text-sm text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Sincronizando dados do admin...</span>
            </div>
          )}

          {syncStatus === 'error' && (
            <div className="text-sm text-red-600">
              <p>❌ Erro na sincronização</p>
              <p>Tente novamente</p>
            </div>
          )}

          {isOnline && syncStatus !== 'syncing' && (
            <Button
              onClick={syncData}
              size="sm"
              className="w-full bg-[#269AB6] hover:bg-[#1e7a94]"
              disabled={syncStatus === 'syncing' as any}
            >
              🔄 Sincronizar Dados
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}