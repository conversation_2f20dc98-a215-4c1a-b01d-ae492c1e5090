import { obterPercentualPorPontuacao } from './pontuacao-fatores';

export interface CalculationResult {
  pontuacaoTotal: number
  percentualFator: number
  faturamento2025: number
  faturamento2026: number
  margemLucro: number
  lucroTotal10Anos: number
  valorEstimado: number
  projecaoAnual: number[]
}

export interface RespostaCalculo {
  perguntaId: string
  valor?: number           // Para respostas de radio/checkbox
  valorNumerico?: number   // Para respostas numéricas
  ordem: number            // Ordem da pergunta para identificação (legado)
  role?: 'FATURAMENTO_2025' | 'FATURAMENTO_2026' | 'MARGEM_LUCRO' | 'SEGUIDORES' // Preferível
}

export class MarcaCalculator {
  
  /**
   * Calcula o valor da marca baseado nas respostas
   * Implementa a nova regra de negócio com projeção 10 anos
   */
  static calcularValorMarca(respostas: RespostaCalculo[]): CalculationResult {
    // Extrair dados específicos por pergunta utilizando role quando disponível
    const faturamento2025 = this.obterValorNumericoPorRole(respostas, 'FATURAMENTO_2025')
      ?? this.obterValorNumerico(respostas, 7)
      ?? 0
    const faturamento2026 = this.obterValorNumericoPorRole(respostas, 'FATURAMENTO_2026')
      ?? this.obterValorNumerico(respostas, 8)
      ?? 0
    const margemLucro = this.obterValorNumericoPorRole(respostas, 'MARGEM_LUCRO')
      ?? this.obterValorNumerico(respostas, 9)
      ?? 0
    const seguidores = this.obterValorNumericoPorRole(respostas, 'SEGUIDORES')
      ?? this.obterValorNumerico(respostas, 12)
      ?? 0
    
    // Calcular pontuação total (perguntas 10-16)
    let pontuacaoTotal = 0
    
    // Perguntas 10, 13, 14, 15, 16 (radio simples)
    const perguntasRadio = [10, 13, 14, 15, 16]
    perguntasRadio.forEach(ordem => {
      const resposta = respostas.find(r => r.ordem === ordem)
      if (resposta?.valor) {
        pontuacaoTotal += resposta.valor
      }
    })
    
    // Pergunta 11 (radio evolutiva - corrigida)
    const pergunta11 = respostas.find(r => r.ordem === 11)
    if (pergunta11?.valor) {
      pontuacaoTotal += pergunta11.valor
    }
    
    // Pergunta 12 (seguidores - fórmula especial)
    const pontuacaoSeguidores = (seguidores / 10000) + 1
    pontuacaoTotal += pontuacaoSeguidores
    
    // Obter percentual da tabela (com teto de 33)
    const percentualFator = obterPercentualPorPontuacao(pontuacaoTotal)
    
    // Calcular projeção de faturamento 10 anos
    const projecaoAnual = this.calcularProjecaoFaturamento(faturamento2025, faturamento2026)
    
    // Calcular lucro total 10 anos
    const lucroTotal10Anos = projecaoAnual.reduce((total, faturamentoAno) => {
      return total + (faturamentoAno * (margemLucro / 100))
    }, 0)
    
    // Valor final da marca
    const valorEstimado = lucroTotal10Anos * percentualFator
    
    return {
      pontuacaoTotal,
      percentualFator,
      faturamento2025,
      faturamento2026,
      margemLucro,
      lucroTotal10Anos,
      valorEstimado,
      projecaoAnual
    }
  }
  
  /**
   * Calcula projeção de faturamento para 10 anos
   * Implementa a fórmula correta conforme documento oficial
   */
  private static calcularProjecaoFaturamento(ano1: number, ano2: number): number[] {
    if (ano1 <= 0 || ano2 <= 0) return Array(10).fill(0)

    // Crescimento base p (decimal). Pode ser negativo conforme entrada
    let p = (ano2 / ano1) - 1

    const projecao: number[] = []
    projecao[0] = ano1 // Ano 1
    projecao[1] = ano2 // Ano 2 (crescimento aplicado = p)

    // Vamos controlar o percentual aplicado em cada ano (a partir do Ano 2)
    // percentuais[i] representa o crescimento aplicado para chegar no Ano i (0-index: Ano 1 -> i=0)
    const percentuais: number[] = Array(10).fill(0)
    percentuais[1] = p // aplicado para chegar no Ano 2

    // Ano 3 = Ano 2 × (1 + p)
    percentuais[2] = p
    projecao[2] = projecao[1] * (1 + percentuais[2])

    // Ano 4 = Ano 3 × (1 + p/2)
    percentuais[3] = p / 2
    projecao[3] = projecao[2] * (1 + percentuais[3])

    // Ano 5 = Ano 4 × (1 + percentual do Ano 4)
    percentuais[4] = percentuais[3]
    projecao[4] = projecao[3] * (1 + percentuais[4])

    // Ano 6 = Ano 5 × (1 + percentual do Ano 5 × 0.15)
    percentuais[5] = percentuais[4] * 0.15
    projecao[5] = projecao[4] * (1 + percentuais[5])

    // Anos 7 a 10 = aplicar sempre o mesmo percentual do Ano 6
    for (let i = 6; i < 10; i++) {
      percentuais[i] = percentuais[5]
      projecao[i] = projecao[i - 1] * (1 + percentuais[i])
    }

    return projecao
  }
  
  /**
   * Obtém valor numérico de uma pergunta específica
   */
  private static obterValorNumerico(respostas: RespostaCalculo[], ordem: number): number | null {
    const resposta = respostas.find(r => r.ordem === ordem)
    return resposta?.valorNumerico ?? null
  }

  private static obterValorNumericoPorRole(respostas: RespostaCalculo[], role: RespostaCalculo['role']): number | null {
    const resposta = respostas.find(r => r.role === role)
    return resposta?.valorNumerico ?? null
  }
  
  /**
   * Formatar valor em Real brasileiro
   */
  static formatarMoeda(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor)
  }
  
  /**
   * Formatar percentual
   */
  static formatarPercentual(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(valor)
  }
  
  /**
   * Formatar projeção de anos para exibição
   */
  static formatarProjecao(projecaoAnual: number[]): string {
    return projecaoAnual
      .map((valor, index) => `Ano ${index + 1}: ${this.formatarMoeda(valor)}`)
      .join('\n')
  }
  
  /**
   * Obter resumo do cálculo para exibição
   */
  static obterResumoCalculo(resultado: CalculationResult): string {
    const { 
      pontuacaoTotal, 
      percentualFator, 
      lucroTotal10Anos, 
      valorEstimado,
      faturamento2025,
      faturamento2026,
      margemLucro
    } = resultado
    
    return `
📊 RESUMO DO VALUATION:
• Faturamento 2025: ${this.formatarMoeda(faturamento2025)}
• Faturamento 2026: ${this.formatarMoeda(faturamento2026)}
• Margem de Lucro: ${margemLucro}%
• Lucro Total (10 anos): ${this.formatarMoeda(lucroTotal10Anos)}
• Pontuação da Marca: ${pontuacaoTotal.toFixed(2)} pontos
• Percentual Aplicado: ${this.formatarPercentual(percentualFator)}
• 🎯 VALOR DA MARCA: ${this.formatarMoeda(valorEstimado)}
    `.trim()
  }
}