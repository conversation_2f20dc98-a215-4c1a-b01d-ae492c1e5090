import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl

  if (pathname.startsWith('/kiosk/offline')) {
    const token = request.cookies.get('kiosk_auth')?.value
    const isLogin = request.nextUrl.searchParams.get('login') === '1'
    if (!token && !isLogin) {
      const url = request.nextUrl.clone()
      url.searchParams.set('login', '1')
      return NextResponse.redirect(url)
    }
  }

  if (pathname.startsWith('/admin/perguntas')) {
    const token = request.cookies.get('admin_auth')?.value
    const isLogin = request.nextUrl.searchParams.get('login') === '1'
    if (!token && !isLogin) {
      const url = request.nextUrl.clone()
      url.searchParams.set('login', '1')
      return NextResponse.redirect(url)
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/kiosk/offline/:path*', '/admin/perguntas/:path*']
}


