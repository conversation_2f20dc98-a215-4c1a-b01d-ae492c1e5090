"use client"

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface RadioOption {
  value: string
  label: string
}

interface RadioGroupProps {
  name: string
  options: RadioOption[]
  value?: string
  onChange?: (value: string) => void
  error?: string
  className?: string
}

const RadioGroup = forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ name, options, value, onChange, error, className }, ref) => {
    return (
      <div ref={ref} className={cn('space-y-3', className)}>
        {options.map((option) => (
          <label
            key={option.value}
            className={cn(
              'flex items-start space-x-3 p-4 rounded-lg border cursor-pointer transition-colors',
              'hover:bg-gray-50',
              value === option.value 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200',
              error && 'border-red-300'
            )}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange?.(e.target.value)}
              className="mt-1 h-4 w-4 text-[#269AB6] border-gray-300 focus:ring-[#269AB6]"
            />
            <span className="text-sm text-gray-800 leading-relaxed font-medium">
              {option.label}
            </span>
          </label>
        ))}
        {error && (
          <p className="text-sm text-red-600 mt-2">{error}</p>
        )}
      </div>
    )
  }
)

RadioGroup.displayName = 'RadioGroup'

export { RadioGroup }