import { sheets } from '@/config/sheets'

type AppendRowInput = {
  spreadsheetId: string
  sheetName?: string
  values: (string | number | null)[]
}

export class GoogleSheetsService {
  static async appendRow({ spreadsheetId, sheetName = 'RAW', values }: AppendRowInput) {
    // Monta o range no formato "SheetName!A1"
    const range = `${sheetName}!A2`

    await sheets.spreadsheets.values.append({
      spreadsheetId,
      range,
      valueInputOption: 'USER_ENTERED',
      insertDataOption: 'INSERT_ROWS',
      requestBody: {
        values: [values]
      }
    })
  }
}


