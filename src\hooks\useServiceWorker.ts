"use client"

import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'

export function useServiceWorker() {
  const [isSupported, setIsSupported] = useState(false)
  const [isRegistered, setIsRegistered] = useState(false)
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null)

  useEffect(() => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      setIsSupported(true)
      // Registrar SW somente em modo kiosk ou quando já estiver em standalone (PWA instalado)
      const isStandalone = window.matchMedia?.('(display-mode: standalone)').matches || (window as any).navigator?.standalone
      const isKioskMode = window.location.pathname.startsWith('/kiosk')
      if (isStandalone || isKioskMode) {
        registerServiceWorker()
      }
    }
  }, [])

  const registerServiceWorker = async () => {
    try {
      const reg = await navigator.serviceWorker.register('/sw-custom.js', {
        scope: '/'
      })

      setRegistration(reg)
      setIsRegistered(true)

      console.log('Service Worker registrado:', reg.scope)

      // Listener para atualizações do SW
      reg.addEventListener('updatefound', () => {
        const newWorker = reg.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // Nova versão disponível
              toast.success('Nova versão disponível! Recarregue a página.')
            }
          })
        }
      })

      // Listener para mensagens do SW
      navigator.serviceWorker.addEventListener('message', event => {
        console.log('Mensagem do Service Worker:', event.data)
        
        if (event.data.type === 'SYNC_COMPLETE') {
          toast.success('Dados sincronizados em background!')
        }
      })

      // Verificar se há nova versão
      reg.update()

    } catch (error) {
      console.error('Falha ao registrar Service Worker:', error)
      setIsRegistered(false)
    }
  }

  // Trigger background sync
  const triggerBackgroundSync = async (tag: string) => {
    if (registration && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        await (registration as any).sync.register(tag)
        console.log('Background sync registrado:', tag)
      } catch (error) {
        console.error('Falha no background sync:', error)
      }
    }
  }

  // Forçar atualização do SW
  const updateServiceWorker = () => {
    if (registration) {
      registration.update()
    }
  }

  return {
    isSupported,
    isRegistered,
    registration,
    triggerBackgroundSync,
    updateServiceWorker
  }
}