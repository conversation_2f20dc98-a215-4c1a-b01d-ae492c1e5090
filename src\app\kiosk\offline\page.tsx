'use client'

import { useEffect, useState } from 'react'
import { useOfflineStatus } from '@/hooks/useOfflineStatus'
import { OfflineService } from '@/lib/indexeddb'
import { SyncManager, type SyncResult } from '@/lib/sync-manager'
import { Toaster, toast } from 'react-hot-toast'

export default function KioskOfflinePage() {
  const { isOnline } = useOfflineStatus()
  const [pinOk, setPinOk] = useState(false)
  const [pin, setPin] = useState('')
  const [pending, setPending] = useState<any[]>([])
  const [synced, setSynced] = useState<any[]>([])
  const [syncing, setSyncing] = useState(false)
  const [lastResult, setLastResult] = useState<SyncResult | null>(null)

  const totalPending = pending.length
  const totalSynced = synced.length

  const loadData = async () => {
    const [p, s] = await Promise.all([
      OfflineService.getPendingLeads(),
      OfflineService.getSyncedLeads(),
    ])
    setPending(p)
    setSynced(s)
  }

  useEffect(() => {
    if (pinOk) {
      loadData()
    }
  }, [pinOk])

  useEffect(() => {
    const i = setInterval(() => {
      if (pinOk) loadData()
    }, 5000)
    return () => clearInterval(i)
  }, [pinOk])

  const handleSync = async () => {
    setSyncing(true)
    const result = await SyncManager.fullSync()
    setLastResult(result)
    setSyncing(false)
    await loadData()
  }

  const disabled = !isOnline || syncing || totalPending === 0

  const doLogin = async () => {
    try {
      const res = await fetch('/api/kiosk/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pin })
      })
      if (res.ok) {
        setPinOk(true)
        setPin('')
        await loadData()
      } else {
        toast.error('PIN inválido')
      }
    } catch (e) {
      toast.error('Falha no login')
    }
  }

  if (!pinOk) {
    return (
      <main className="min-h-screen bg-[var(--primary-bg)] text-white flex items-center justify-center p-6">
        <div className="w-full max-w-sm bg-white/10 backdrop-blur rounded-xl p-6 border border-white/20">
          <h1 className="text-2xl font-bold mb-4">Acesso Offline (Staff)</h1>
          <p className="text-sm text-white/80 mb-4">Digite o PIN para visualizar e sincronizar os leads.</p>
          <input
            type="password"
            className="w-full p-3 rounded bg-white text-black mb-3"
            placeholder="PIN"
            value={pin}
            onChange={(e) => setPin(e.target.value)}
            onKeyDown={(e) => { if (e.key === 'Enter') doLogin() }}
          />
          <button
            className="w-full py-3 rounded font-semibold bg-black/70 hover:bg-black/80"
            onClick={doLogin}
          >
            Entrar
          </button>
          <p className="text-xs mt-3 text-white/70">Conexão: {isOnline ? 'Online' : 'Offline'}</p>
        </div>
        <Toaster position="top-right" />
      </main>
    )
  }

  return (
    <main className="min-h-screen bg-[var(--primary-bg)] text-white p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <h1 className="text-2xl font-bold">Leads Offline</h1>
          <div className="flex items-center gap-3">
            <span className={`px-3 py-1 rounded-full text-sm ${isOnline ? 'bg-green-600' : 'bg-red-600'}`}>
              {isOnline ? 'Conectado' : 'Sem conexão'}
            </span>
            <button
              className={`px-4 py-2 rounded font-semibold ${disabled ? 'bg-white/30 cursor-not-allowed' : 'bg-black/70 hover:bg-black/80'}`}
              disabled={disabled}
              onClick={handleSync}
            >
              {syncing ? 'Sincronizando...' : 'Sincronizar agora'}
            </button>
            <form action="/api/kiosk/logout" method="post">
              <button className="px-3 py-2 rounded font-semibold bg-white/20 hover:bg-white/30" type="submit">
                Sair
              </button>
            </form>
          </div>
        </div>

        {/* Resumo */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/10 border border-white/20 rounded-xl p-4">
            <p className="text-sm text-white/80">Pendentes</p>
            <p className="text-3xl font-bold">{totalPending}</p>
          </div>
          <div className="bg-white/10 border border-white/20 rounded-xl p-4">
            <p className="text-sm text-white/80">Sincronizados</p>
            <p className="text-3xl font-bold">{totalSynced}</p>
          </div>
          {lastResult && (
            <div className="bg-white/10 border border-white/20 rounded-xl p-4 col-span-2">
              <p className="text-sm text-white/80">Última sincronização</p>
              <p className="text-sm">Sucesso: {lastResult.success} | Falhas: {lastResult.failed} | Total: {lastResult.total}</p>
            </div>
          )}
        </div>

        {/* Lista de pendentes */}
        <div className="bg-white/10 border border-white/20 rounded-xl p-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold">Pendentes de sincronização</h2>
          </div>
          {pending.length === 0 ? (
            <p className="text-white/80">Nenhum lead pendente.</p>
          ) : (
            <div className="overflow-auto max-h-[50vh]">
              <table className="w-full text-left text-sm">
                <thead className="sticky top-0 bg-[var(--primary-bg)]">
                  <tr>
                    <th className="py-2 pr-2">#</th>
                    <th className="py-2 pr-2">Marca</th>
                    <th className="py-2 pr-2">Nome</th>
                    <th className="py-2 pr-2">Email</th>
                    <th className="py-2 pr-2">Criado em</th>
                  </tr>
                </thead>
                <tbody>
                  {pending.map((l, idx) => (
                    <tr key={l.id} className="border-t border-white/10">
                      <td className="py-2 pr-2">{idx + 1}</td>
                      <td className="py-2 pr-2">{l.nomeMarca}</td>
                      <td className="py-2 pr-2">{l.nome}</td>
                      <td className="py-2 pr-2">{l.email}</td>
                      <td className="py-2 pr-2">{new Date(l.createdAt).toLocaleString('pt-BR')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Toggle lista de sincronizados */}
        <details className="bg-white/10 border border-white/20 rounded-xl p-4">
          <summary className="cursor-pointer select-none">Mostrar sincronizados ({totalSynced})</summary>
          {synced.length === 0 ? (
            <p className="text-white/80 mt-3">Nenhum lead sincronizado neste dispositivo.</p>
          ) : (
            <div className="overflow-auto max-h-[40vh] mt-3">
              <table className="w-full text-left text-sm">
                <thead className="sticky top-0 bg-[var(--primary-bg)]">
                  <tr>
                    <th className="py-2 pr-2">#</th>
                    <th className="py-2 pr-2">Marca</th>
                    <th className="py-2 pr-2">Nome</th>
                    <th className="py-2 pr-2">Email</th>
                    <th className="py-2 pr-2">Criado em</th>
                  </tr>
                </thead>
                <tbody>
                  {synced.map((l, idx) => (
                    <tr key={l.id} className="border-t border-white/10">
                      <td className="py-2 pr-2">{idx + 1}</td>
                      <td className="py-2 pr-2">{l.nomeMarca}</td>
                      <td className="py-2 pr-2">{l.nome}</td>
                      <td className="py-2 pr-2">{l.email}</td>
                      <td className="py-2 pr-2">{new Date(l.createdAt).toLocaleString('pt-BR')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </details>
      </div>
    </main>
  )
}


