'use client'

import { PWAInitializer } from '@/components/common/PWAInitializer'
import { PWAInstallPrompt } from '@/components/common/PWAInstallPrompt'
import { MultiStepQuizForm } from '@/components/forms/MultiStepQuizForm'
import { Toaster } from 'react-hot-toast'

export default function KioskPage() {
  return (
    <>
      <PWAInitializer />
      <main className="min-h-screen bg-[var(--primary-bg)] text-white flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          {/* Direto para o quiz no modo kiosk (offline-enabled) */}
          <MultiStepQuizForm offline={true} />
        </div>
      </main>
      <PWAInstallPrompt />
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: { background: '#1D3623', color: '#fff', border: '1px solid rgba(255, 255, 255, 0.2)' },
        }}
      />
    </>
  )
}



