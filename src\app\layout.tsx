import type { <PERSON>ada<PERSON>, Viewport } from 'next'
import { Ralew<PERSON> } from 'next/font/google'
import './globals.css'

const raleway = Raleway({ 
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
  variable: '--font-raleway'
})

export const metadata: Metadata = {
  metadataBase: new URL('https://sua-marca-tem-valor.vercel.app'),
  title: 'Sua Marca Tem Valor - Avaliação Profissional de Marca',
  description: 'Descubra quanto vale sua marca com nossa ferramenta de avaliação profissional. Funciona offline e sincroniza automaticamente.',
  manifest: '/manifest.json',
  keywords: ['avaliação de marca', 'valor da marca', 'branding', 'marketing', 'empresa'],
  authors: [{ name: 'MarcaValor' }],
  creator: '<PERSON><PERSON><PERSON>alor',
  publisher: 'MarcaValor',
  formatDetection: {
    telephone: false
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'MarcaValor'
  },
  openGraph: {
    type: 'website',
    locale: 'pt_BR',
    title: 'Sua Marca Tem Valor - Avaliação Profissional',
    description: 'Descubra quanto vale sua marca com nossa ferramenta de avaliação profissional. Funciona offline e sincroniza automaticamente.',
    siteName: 'MarcaValor',
    images: [
      {
        url: '/icons/icon-512x512.png',
        width: 512,
        height: 512,
        alt: 'MarcaValor Logo'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sua Marca Tem Valor',
    description: 'Descubra quanto vale sua marca com nossa ferramenta de avaliação profissional.',
    images: ['/icons/icon-512x512.png']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  }
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: '#38C051'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR">
      <head>
        <link rel="manifest" href="/manifest.json" />
        
        {/* PWA Icons */}
        <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/icons/icon-512x512.png" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png" />
        
        {/* iOS Splash Screens */}
        <link rel="apple-touch-startup-image" href="/icons/splash-iphone-x.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-iphone-xr.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-iphone-xs-max.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-iphone-8.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-iphone-8-plus.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-ipad.png" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" />
        <link rel="apple-touch-startup-image" href="/icons/splash-ipad-pro.png" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" />
        
        {/* PWA Meta Tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="MarcaValor" />
        <meta name="application-name" content="MarcaValor" />
        <meta name="msapplication-TileColor" content="#38C051" />
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
        
        {/* Preload Critical Resources */}
        {/* Removido preload da API pois pode não ser usado imediatamente */}
      </head>
      <body className={`${raleway.className} ${raleway.variable}`}>
        {children}
        {/* PWA Initializer - não renderiza nada visível */}
        <script dangerouslySetInnerHTML={{
          __html: `
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw-custom.js')
                  .then(reg => console.log('SW registrado:', reg.scope))
                  .catch(err => console.log('Erro no SW:', err));
              });
            }
          `
        }} />
      </body>
    </html>
  )
}