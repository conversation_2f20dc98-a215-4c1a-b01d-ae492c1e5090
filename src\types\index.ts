// Tipos principais da aplicação

export interface Pergunta {
  id: string
  texto: string
  tipo: string
  multiplicador: boolean
  ordem: number
  createdAt: Date
  updatedAt: Date
  opcoes: OpcaoResposta[]
  // Configuração adicional para tornar o formulário editável e resiliente
  // Exemplos:
  // - numberFormat: 'currency' | 'percentage' | 'followers'
  // - role: 'FATURAMENTO_2025' | 'FATURAMENTO_2026' | 'MARGEM_LUCRO' | 'SEGUIDORES'
  // - pontua: boolean (se contribui para a pontuação)
  config?: Record<string, any>
}

export interface OpcaoResposta {
  id: string
  label: string
  valor: number
  perguntaId: string
  createdAt: Date
}

export interface Lead {
  id: string
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  pontuacaoTotal: number
  percentualFator: number
  valorEstimado: number
  rdstationId?: string | null  // ID do lead no RD Station
  crmId?: string | null        // ID do lead no CRM
  createdAt: Date
  updatedAt: Date
  respostas: RespostaLead[]
}

export interface RespostaLead {
  id: string
  leadId: string
  perguntaId: string
  opcaoRespostaId?: string  // Para radio/checkbox
  valorNumerico?: number    // Para NUMBER
  valorTexto?: string       // Para TEXT
  createdAt: Date
  pergunta: Pergunta
  opcaoResposta?: OpcaoResposta  // Opcional para respostas numéricas
}

// Tipos para formulários
export interface FormularioLead {
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  respostas: { [perguntaId: string]: string | number | string[] } // Suporta também arrays para CHECKBOX
}

// Tipos para resultados
export interface ResultadoAvaliacao {
  pontuacaoTotal: number
  percentualFator: number
  mediaFaturamento: number
  valorEstimado: number
  faixaFaturamento: string
}

// Status de sincronização
export type StatusSincronizacao = 'online' | 'offline' | 'sincronizando'