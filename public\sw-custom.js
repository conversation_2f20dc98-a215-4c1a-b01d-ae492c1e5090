// Service Worker customizado para PWA Sua Marca Tem Valor

const CACHE_NAME = 'marca-valor-v2'
const urlsToCache = [
  '/kiosk',
  '/kiosk/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('SW: Install event')
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('SW: Opened cache')
        return cache.addAll(urlsToCache)
      })
  )
  self.skipWaiting()
})

// Activate event - cleanup old caches
self.addEventListener('activate', event => {
  console.log('SW: Activate event')
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('SW: Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', event => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return

  // Skip chrome-extension requests
  if (event.request.url.startsWith('chrome-extension://')) return

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
          .then(fetchResponse => {
            // Don't cache API calls or admin routes
            if (event.request.url.includes('/api/') || 
                event.request.url.includes('/admin/')) {
              return fetchResponse
            }

            // Clone the response
            const responseToCache = fetchResponse.clone()

            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache)
              })

            return fetchResponse
          })
          .catch(() => {
            // Fallback for navigation requests
            if (event.request.mode === 'navigate') {
              return caches.match('/kiosk')
            }
          })
      })
  )
})

// Background Sync event
self.addEventListener('sync', event => {
  console.log('SW: Background sync event:', event.tag)
  
  if (event.tag === 'sync-leads') {
    event.waitUntil(syncLeads())
  }
})

// Sync leads function
async function syncLeads() {
  try {
    console.log('SW: Syncing leads...')
    
    // This would normally communicate with your sync manager
    // For now, just notify the main thread
    const clients = await self.clients.matchAll()
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_REQUEST',
        tag: 'sync-leads'
      })
    })
    
  } catch (error) {
    console.error('SW: Error syncing leads:', error)
  }
}

// Message handler
self.addEventListener('message', event => {
  console.log('SW: Received message:', event.data)
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

console.log('SW: Service Worker loaded')