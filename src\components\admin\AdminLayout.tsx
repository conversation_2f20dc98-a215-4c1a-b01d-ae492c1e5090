"use client"

import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { AdminSync } from './AdminSync'

interface AdminLayoutProps {
  children: React.ReactNode
  title?: string
}

export function AdminLayout({ children, title = 'Painel Admin' }: AdminLayoutProps) {
  const { session, loading, isAuthenticated, logout } = useAdminAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!loading && !isAuthenticated && pathname !== '/admin/login') {
      router.push('/admin/login')
    }
  }, [loading, isAuthenticated, pathname, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#269AB6] mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated && pathname !== '/admin/login') {
    return null
  }

  if (pathname === '/admin/login') {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/admin" className="text-xl font-bold text-[#269AB6]">
                🏢 Admin - Sua Marca Tem Valor
              </Link>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                👋 Olá, {session?.username}
              </span>
              <Button
                onClick={logout}
                variant="secondary"
                size="sm"
              >
                Sair
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-[#269AB6] shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <Link
              href="/admin"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                pathname === '/admin'
                  ? 'border-white text-white'
                  : 'border-transparent text-blue-100 hover:text-white hover:border-blue-200'
              }`}
            >
              📊 Dashboard
            </Link>
            
            <Link
              href="/admin/perguntas"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                pathname === '/admin/perguntas'
                  ? 'border-white text-white'
                  : 'border-transparent text-blue-100 hover:text-white hover:border-blue-200'
              }`}
            >
              ❓ Perguntas
            </Link>
            
            <Link
              href="/admin/leads"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                pathname === '/admin/leads'
                  ? 'border-white text-white'
                  : 'border-transparent text-blue-100 hover:text-white hover:border-blue-200'
              }`}
            >
              👥 Leads
            </Link>

            <Link
              href="/admin/integracoes"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                pathname === '/admin/integracoes'
                  ? 'border-white text-white'
                  : 'border-transparent text-blue-100 hover:text-white hover:border-blue-200'
              }`}
            >
              🔗 Integrações
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        </div>
        
        <div className="bg-white rounded-lg shadow">
          {children}
        </div>
      </main>
      
      {/* Sync Status para Admin */}
      <AdminSync />
    </div>
  )
}