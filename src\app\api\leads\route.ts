import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { MarcaCalculator } from '@/lib/calculations'
// Integrações externas (CRM/RD) removidas por ora; usamos apenas Google Sheets
import { GoogleSheetsService } from '@/lib/google-sheets'

interface LeadRequest {
  clientId?: string
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  respostas: { [perguntaId: string]: string | number | string[] } // agora suporta arrays para checkbox
  // Campos opcionais do cálculo (vindos do offline)
  pontuacaoTotal?: number
  percentualFator?: number
  valorEstimado?: number
  faturamento2025?: number
  faturamento2026?: number
  margemLucro?: number
  lucroTotal10Anos?: number
}

export async function POST(request: NextRequest) {
  try {
    const body: LeadRequest = await request.json()
    
    // Validações básicas
    if (!body.nomeMarca || !body.nome || !body.whatsapp || !body.email) {
      return NextResponse.json(
        { error: 'Campos obrigatórios não preenchidos' },
        { status: 400 }
      )
    }

    if (!body.respostas || Object.keys(body.respostas).length === 0) {
      return NextResponse.json(
        { error: 'Nenhuma resposta fornecida' },
        { status: 400 }
      )
    }

    // Idempotência por clientId (evitar duplicatas em reenvios)
    if (body.clientId) {
      const existing = await prisma.lead.findFirst({ where: { clientId: body.clientId } as any })
      if (existing) {
        return NextResponse.json({ leadId: existing.id }, { status: 200 })
      }
    }

    // Buscar perguntas e opções para cálculo
    const perguntas = await prisma.pergunta.findMany({
      include: {
        opcoes: true
      }
    })

    // Preparar dados para cálculo com nova estrutura
    const respostasCalculo = Object.entries(body.respostas).map(([perguntaId, valorResposta]) => {
      const pergunta = perguntas.find(p => p.id === perguntaId)
      
      if (!pergunta) {
        throw new Error(`Pergunta não encontrada: ${perguntaId}`)
      }

      // Para perguntas NUMBER (7, 8, 9, 12)
      if (pergunta.tipo === 'NUMBER') {
        const valorNumerico = typeof valorResposta === 'number' 
          ? valorResposta 
          : parseFloat(valorResposta as string)
          
        if (isNaN(valorNumerico)) {
          throw new Error(`Valor numérico inválido para pergunta ${perguntaId}: ${valorResposta}`)
        }
        
        return {
          perguntaId,
          valorNumerico,
          ordem: pergunta.ordem
        }
      }
      
      // Para perguntas CHECKBOX: somar valores das opções marcadas
      if (pergunta.tipo === 'CHECKBOX') {
        const ids = Array.isArray(valorResposta) ? valorResposta as string[] : [String(valorResposta)]
        const valores = ids.map(id => {
          const op = pergunta.opcoes.find(o => o.id === id)
          if (!op) throw new Error(`Opção não encontrada: ${perguntaId}/${id}`)
          return op.valor
        })
        const soma = valores.reduce((s, v) => s + v, 0)
        return { perguntaId, valor: soma, ordem: pergunta.ordem }
      }

      // Para perguntas RADIO (5, 6, 10-16)
      const opcao = pergunta.opcoes.find(o => o.id === valorResposta)
      if (!opcao) {
        throw new Error(`Opção não encontrada: ${perguntaId}/${valorResposta}`)
      }
      return { perguntaId, valor: opcao.valor, ordem: pergunta.ordem }
    })

    // Calcular valor da marca com nova lógica
    const resultado = MarcaCalculator.calcularValorMarca(respostasCalculo)

    // Criar lead no banco com nova estrutura
    const lead = await prisma.lead.create({
      data: {
        ...(body.clientId ? ({ clientId: body.clientId } as any) : {}),
        nomeMarca: body.nomeMarca,
        nome: body.nome,
        whatsapp: body.whatsapp,
        email: body.email,
        pontuacaoTotal: resultado.pontuacaoTotal,
        percentualFator: resultado.percentualFator,
        valorEstimado: resultado.valorEstimado,
        respostas: {
          // gerar payload de respostas aceitando NUMBER (objeto) e RADIO/CHECKBOX (array de objetos)
          create: (Object.entries(body.respostas).flatMap(([perguntaId, valorResposta]): Array<{ perguntaId: string; valorNumerico?: number; opcaoRespostaId?: string }> => {
            const pergunta = perguntas.find(p => p.id === perguntaId)
            
            // Para perguntas NUMBER
            if (pergunta?.tipo === 'NUMBER') {
              const valorNumerico = typeof valorResposta === 'number' 
                ? valorResposta 
                : parseFloat(valorResposta as string)
                
              const one: { perguntaId: string; valorNumerico: number } = {
                perguntaId,
                valorNumerico
              }
              return [one]
            }
            // Para perguntas CHECKBOX: criar múltiplas respostas
            if (pergunta?.tipo === 'CHECKBOX') {
              const ids = Array.isArray(valorResposta) ? valorResposta as string[] : [String(valorResposta)]
              return ids.map(id => ({ perguntaId, opcaoRespostaId: id }))
            }
            // Para perguntas RADIO
            return [{ perguntaId, opcaoRespostaId: valorResposta as string }]
          }))
        }
      },
      include: {
        respostas: {
          include: {
            pergunta: true,
            opcaoResposta: true
          }
        }
      }
    })

    // Integrações CRM/RD desativadas; mantemos apenas o envio para Google Sheets

    // Enviar para Google Sheets em background (não bloquear resposta)
    ;(async () => {
      try {
        const spreadsheetId = process.env.GOOGLE_SHEETS_ID || '1DAnTYSsFzSb4LlU748I5uoYAc8sApCGkgBj1-hF3OKA'
        if (!spreadsheetId) {
          console.warn('GOOGLE_SHEETS_ID não configurado')
          return
        }

        // Helpers de formatação
        const formatCurrencyBR = (value: number | null | undefined) =>
          typeof value === 'number'
            ? new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value)
            : ''

        const formatPercentBR = (value: number | null | undefined) =>
          typeof value === 'number'
            ? `${new Intl.NumberFormat('pt-BR', { maximumFractionDigits: 2 }).format(value)}%`
            : ''

        const formatFollowersBR = (value: number | null | undefined) =>
          typeof value === 'number' ? new Intl.NumberFormat('pt-BR').format(value) : ''

        const formatTimestampBR = () =>
          new Intl.DateTimeFormat('pt-BR', {
            timeZone: 'America/Sao_Paulo',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).format(new Date())

        // Mapear respostas em texto por ordem das perguntas (5..16)
        const respostasTexto: Record<number, string> = {}
        for (let ordem = 5; ordem <= 16; ordem++) {
          const pergunta = perguntas.find(p => p.ordem === ordem)
          if (!pergunta) {
            respostasTexto[ordem] = ''
            continue
          }

          const respostaValor = body.respostas[pergunta.id]
          if (respostaValor === undefined || respostaValor === null) {
            respostasTexto[ordem] = ''
            continue
          }

          if (pergunta.tipo === 'NUMBER') {
            const num = typeof respostaValor === 'number' ? respostaValor : parseFloat(String(respostaValor))
            if (Number.isNaN(num)) {
              respostasTexto[ordem] = ''
              continue
            }
            if (ordem === 7 || ordem === 8) {
              // Faturamentos → moeda BR
              respostasTexto[ordem] = formatCurrencyBR(num)
            } else if (ordem === 9) {
              // Margem → percentual
              respostasTexto[ordem] = formatPercentBR(num)
            } else if (ordem === 12) {
              // Seguidores → número com milhares
              respostasTexto[ordem] = formatFollowersBR(num)
            } else {
              respostasTexto[ordem] = new Intl.NumberFormat('pt-BR').format(num)
            }
          } else {
            // RADIO → label
            const opcao = pergunta.opcoes.find(o => o.id === respostaValor)
            respostasTexto[ordem] = opcao?.label || ''
          }
        }

        // Timestamp no fuso de São Paulo e resultado em moeda BR
        const timestamp = formatTimestampBR()
        const valorEstimadoBRL = formatCurrencyBR(resultado.valorEstimado)

        // Montar linha no padrão: [timestamp, leadId, valorEstimadoBRL, Q1..Q16]
        const row = [
          timestamp,
          lead.id,
          valorEstimadoBRL,
          // Q1..Q4 (dados pessoais)
          lead.nomeMarca,
          lead.nome,
          lead.whatsapp,
          lead.email,
          // Q5..Q16 (texto/formatado)
          respostasTexto[5] || '',
          respostasTexto[6] || '',
          respostasTexto[7] || '',
          respostasTexto[8] || '',
          respostasTexto[9] || '',
          respostasTexto[10] || '',
          respostasTexto[11] || '',
          respostasTexto[12] || '',
          respostasTexto[13] || '',
          respostasTexto[14] || '',
          respostasTexto[15] || '',
          respostasTexto[16] || ''
        ]

        await GoogleSheetsService.appendRow({ spreadsheetId, sheetName: 'RAW', values: row })
        // Atualiza metadados de sync no banco (best-effort)
        await prisma.lead.update({
          where: { id: lead.id },
          data: { syncedToSheetsAt: new Date() } as any
        })
      } catch (error) {
        console.error('Erro ao enviar para Google Sheets:', error)
      }
    })()

    // Retornar resultado com nova estrutura
    return NextResponse.json({
      leadId: lead.id,
      resultado: {
        pontuacaoTotal: resultado.pontuacaoTotal,
        percentualFator: resultado.percentualFator,
        faturamento2025: resultado.faturamento2025,
        faturamento2026: resultado.faturamento2026,
        margemLucro: resultado.margemLucro,
        lucroTotal10Anos: resultado.lucroTotal10Anos,
        valorEstimado: resultado.valorEstimado,
        valorFormatado: MarcaCalculator.formatarMoeda(resultado.valorEstimado),
        percentualFormatado: MarcaCalculator.formatarPercentual(resultado.percentualFator),
        resumoCalculo: MarcaCalculator.obterResumoCalculo(resultado),
        projecaoAnual: resultado.projecaoAnual
      }
    })

  } catch (error) {
    console.error('Erro ao processar lead:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Endpoint para listar leads (admin)
    const leads = await prisma.lead.findMany({
      include: {
        respostas: {
          include: {
            pergunta: true,
            opcaoResposta: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50 // Limitar a 50 leads mais recentes
    })

    return NextResponse.json(leads)
  } catch (error) {
    console.error('Erro ao buscar leads:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}