"use client"

import { useState, useEffect } from 'react'
import { OfflineService, IndexedDBTester } from '@/lib/indexeddb'
import { SyncManager } from '@/lib/sync-manager'

export default function DebugPage() {
  const [logs, setLogs] = useState<string[]>([])
  const [isOnline, setIsOnline] = useState(false)
  const [hasServiceWorker, setHasServiceWorker] = useState(false)
  const [hasIndexedDB, setHasIndexedDB] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `${timestamp}: ${message}`])
    console.log(message)
  }

  useEffect(() => {
    // Verificar se estamos no cliente
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      setIsOnline(navigator.onLine)
      setHasServiceWorker('serviceWorker' in navigator)
      setHasIndexedDB(!!window.indexedDB)
      
      const handleOnline = () => {
        setIsOnline(true)
        addLog('🌐 Conexão restaurada')
      }
      
      const handleOffline = () => {
        setIsOnline(false)
        addLog('📴 Conexão perdida')
      }

      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)

      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
      }
    }
  }, [])

  const testIndexedDB = async () => {
    addLog('🧪 Testando IndexedDB...')
    const result = await IndexedDBTester.testConnection()
    addLog(result ? '✅ IndexedDB funcionando' : '❌ IndexedDB com problemas')
  }

  const syncQuestions = async () => {
    addLog('🔄 Sincronizando perguntas...')
    const result = await SyncManager.syncQuestions()
    addLog(result ? '✅ Perguntas sincronizadas' : '❌ Falha na sincronização')
  }

  const checkOfflineData = async () => {
    addLog('🔍 Verificando dados offline...')
    const questions = await OfflineService.getQuestionsOffline()
    const leads = await OfflineService.getPendingLeads()
    addLog(`📊 Perguntas offline: ${questions.length}`)
    addLog(`📊 Leads pendentes: ${leads.length}`)
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug PWA - Sua Marca Tem Valor</h1>
        
        <div className="grid md:grid-cols-2 gap-8">
          {/* Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Status</h2>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>{isOnline ? 'Online' : 'Offline'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${hasServiceWorker ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>Service Worker {hasServiceWorker ? 'Suportado' : 'Não Suportado'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${hasIndexedDB ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>IndexedDB {hasIndexedDB ? 'Suportado' : 'Não Suportado'}</span>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Ações de Teste</h2>
            <div className="space-y-3">
              <button
                onClick={testIndexedDB}
                className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600"
              >
                Testar IndexedDB
              </button>
              <button
                onClick={syncQuestions}
                className="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600"
              >
                Sincronizar Perguntas
              </button>
              <button
                onClick={checkOfflineData}
                className="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600"
              >
                Verificar Dados Offline
              </button>
              <button
                onClick={clearLogs}
                className="w-full bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600"
              >
                Limpar Logs
              </button>
            </div>
          </div>
        </div>

        {/* Logs */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Logs de Debug</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded h-96 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <div className="text-gray-500">Nenhum log ainda...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>

        <div className="mt-8 text-center">
          <a
            href="/"
            className="bg-blue-500 text-white py-2 px-6 rounded hover:bg-blue-600"
          >
            Voltar ao App
          </a>
        </div>
      </div>
    </div>
  )
}