"use client"

import { InputHTMLAttributes, forwardRef, useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface NumberInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'value' | 'onChange'> {
  label?: string
  error?: string
  value?: number | string
  onChange?: (value: number | undefined) => void
  formatType?: 'currency' | 'percentage' | 'number'
  placeholder?: string
  min?: number
  max?: number
}

const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(
  ({ 
    className, 
    label, 
    error, 
    id, 
    value, 
    onChange, 
    formatType = 'number',
    placeholder,
    min,
    max,
    ...props 
  }, ref) => {
    const [displayValue, setDisplayValue] = useState('')
    const [focused, setFocused] = useState(false)

    // Formatação baseada no tipo
    const formatValue = (val: number): string => {
      if (isNaN(val)) return ''
      
      switch (formatType) {
        case 'currency':
          return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(val)
        
        case 'percentage':
          return `${val}%`
        
        default:
          return new Intl.NumberFormat('pt-BR').format(val)
      }
    }

    // Parse do valor de entrada
    const parseValue = (str: string): number | undefined => {
      if (!str) return undefined
      
      // Remove caracteres de formatação
      const cleanStr = str
        .replace(/[R$\s]/g, '')
        .replace(/[.]/g, '')
        .replace(/[,]/g, '.')
        .replace(/%/g, '')
      
      const num = parseFloat(cleanStr)
      return isNaN(num) ? undefined : num
    }

    // Sincronizar valor externo com display
    useEffect(() => {
      if (value !== undefined && value !== '') {
        const numValue = typeof value === 'string' ? parseValue(value) : value
        if (numValue !== undefined && !focused) {
          setDisplayValue(formatValue(numValue))
        }
      } else if (!focused) {
        setDisplayValue('')
      }
    }, [value, focused, formatType])

    const handleFocus = () => {
      setFocused(true)
      // Mostrar valor numérico puro no foco
      if (value !== undefined && value !== '') {
        const numValue = typeof value === 'string' ? parseValue(value) : value
        setDisplayValue(numValue?.toString() || '')
      }
    }

    const handleBlur = () => {
      setFocused(false)
      const numValue = parseValue(displayValue)
      
      // Validação de limites
      let finalValue = numValue
      if (finalValue !== undefined) {
        if (min !== undefined && finalValue < min) finalValue = min
        if (max !== undefined && finalValue > max) finalValue = max
      }
      
      // Notificar mudança
      onChange?.(finalValue)
      
      // Atualizar display formatado
      if (finalValue !== undefined) {
        setDisplayValue(formatValue(finalValue))
      } else {
        setDisplayValue('')
      }
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      setDisplayValue(inputValue)
      
      // Se estiver focado, permitir entrada livre
      if (focused) {
        const numValue = parseValue(inputValue)
        onChange?.(numValue)
      }
    }

    // Placeholder baseado no tipo
    const getPlaceholder = (): string => {
      if (placeholder) return placeholder
      
      switch (formatType) {
        case 'currency':
          return 'Ex: 50.000'
        case 'percentage':
          return 'Ex: 25'
        default:
          return 'Digite um número'
      }
    }

    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-gray-800 mb-1">
            {label}
          </label>
        )}
        <div className="relative">
          <input
            className={cn(
              'flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-base text-gray-900 font-medium',
              'placeholder:text-gray-400',
              'focus:border-[#269AB6] focus:outline-none focus:ring-2 focus:ring-[#269AB6]/20',
              'disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50',
              error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20',
              formatType === 'currency' && 'text-right',
              className
            )}
            ref={ref}
            id={id}
            type="text"
            inputMode="numeric"
            value={displayValue}
            placeholder={getPlaceholder()}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />
          {formatType === 'currency' && !focused && (
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">R$</span>
            </div>
          )}
        </div>
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
        {formatType === 'currency' && (
          <p className="text-xs text-gray-500">
            Digite apenas números. Ex: 50000 para R$ 50.000
          </p>
        )}
      </div>
    )
  }
)

NumberInput.displayName = 'NumberInput'

export { NumberInput }