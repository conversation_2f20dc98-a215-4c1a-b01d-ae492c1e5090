"use client"

import { useState, useEffect } from 'react'
import { FormattedInput } from '@/components/ui/FormattedInput'
import { NumberSwipeInput } from '@/components/ui/NumberSwipeInput'
import { OptionButton } from '@/components/ui/OptionButton'
import { useQuizForm } from '@/hooks/useQuizForm'
import { useQuizStore } from '@/stores/quizStore'
import PingIcon from '@/components/forms/PingIconSvg'
import ResultScreen from '@/components/screens/ResultScreen'
import styles from './styles/MultiStepQuizForm.module.css'
import Image from 'next/image'

interface Step {
  id: string
  type: 'personal' | 'question'
  title: string
  subtitle?: string
  fields?: string[]
  perguntaId?: string
}

export function MultiStepQuizForm({ offline = true }: { offline?: boolean }) {
  const { 
    perguntas, 
    loading, 
    submitting, 
    result, 
    isOnline,
    onSubmit,
    submitManually,
    resetQuiz 
  } = useQuizForm({ offline })

  // Store Zustand
  const {
    // Estado
    nomeMarca,
    nome,
    whatsapp,
    email,
    respostas,
    currentStep,
    isTransitioning,
    
    // Actions
    setDadosPessoais,
    setRespostaRadio,
    setRespostaNumber,
    setRespostaCheckbox,
    setCurrentStep,
    setIsTransitioning,
    getResposta,
    getRespostaValor,
    getRespostaOpcaoId,
    validateDadosPessoais,
    validateResposta
  } = useQuizStore()

  const [steps, setSteps] = useState<Step[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Criar steps baseado nas perguntas carregadas
  useEffect(() => {
    if (perguntas.length > 0) {
      const newSteps: Step[] = [
        // Perguntas pessoais individuais
        {
          id: 'nome-marca',
          type: 'personal',
          title: 'Qual o nome da sua marca?',
          subtitle: 'Vamos começar conhecendo sua marca',
          fields: ['nomeMarca']
        },
        {
          id: 'nome-pessoa',
          type: 'personal', 
          title: 'Qual o seu nome?',
          subtitle: 'Agora queremos conhecer você',
          fields: ['nome']
        },
        {
          id: 'whatsapp',
          type: 'personal',
          title: 'Qual o seu WhatsApp?',
          subtitle: 'Para entrarmos em contato',
          fields: ['whatsapp']
        },
        {
          id: 'email',
          type: 'personal',
          title: 'Qual o seu e-mail?',
          subtitle: 'Para enviarmos seu resultado',
          fields: ['email']
        },
        // Perguntas do questionário
        ...perguntas.map((pergunta, index) => ({
          id: pergunta.id,
          type: 'question' as const,
          title: pergunta.texto,
          subtitle: `Pergunta ${index + 1} de ${perguntas.length}`,
          perguntaId: pergunta.id
        }))
      ]
      setSteps(newSteps)
      console.log('📋 Steps criados:', newSteps.length, 'total')
    }
  }, [perguntas])

  // Função para ir ao próximo step com animação
  const goToNextStep = async () => {
    const currentStepData = steps[currentStep]
    
    console.log('🔄 goToNextStep:', { 
      step: currentStep, 
      type: currentStepData?.type, 
      perguntaId: currentStepData?.perguntaId 
    })
    
    if (currentStepData?.type === 'personal') {
      // Validar campo pessoal específico
      const field = currentStepData.fields?.[0]
      if (field) {
        const value = field === 'nomeMarca' ? nomeMarca : 
                     field === 'nome' ? nome : 
                     field === 'whatsapp' ? whatsapp : 
                     field === 'email' ? email : ''
        
        const validation = validateDadosPessoais({ [field]: value })
        if (!validation.success) {
          console.log('❌ Validação falhou:', field, validation.errors)
          
          // 🎯 CORREÇÃO: Extrair apenas a primeira mensagem de erro
          const errorValue = validation.errors?.[field];
          let errorMessage = 'Campo inválido';
          
          if (typeof errorValue === 'string') {
            try {
              // Se é uma string JSON, fazer parse e pegar primeira mensagem
              const parsedErrors = JSON.parse(errorValue);
              if (Array.isArray(parsedErrors) && parsedErrors.length > 0) {
                errorMessage = parsedErrors[0].message;
              }
            } catch {
              // Se não é JSON válido, usar a string como está
              errorMessage = errorValue;
            }
          } else if (errorValue) {
            errorMessage = String(errorValue);
          }
          
          console.log('🔍 DEBUG Mensagem de erro extraída:', errorMessage);
          setErrors({ [field]: errorMessage })
          return
        }
        setErrors({})
      }
    } else if (currentStepData?.type === 'question') {
      // Validar se respondeu a pergunta atual
      const perguntaId = currentStepData.perguntaId
      const resposta = getResposta(perguntaId!)
      
      if (!resposta) {
        console.log('❌ Pergunta não respondida:', perguntaId)
        setErrors({ [perguntaId!]: 'Esta pergunta é obrigatória' })
        return
      }
      setErrors({})
    }

    setIsTransitioning(true)
    
    setTimeout(() => {
      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1)
        console.log('✅ Step avançou para:', currentStep + 1)
      } else {
        // Último step - submeter formulário
        console.log('🎯 Último step - submetendo formulário')
        submitManually()
      }
      setIsTransitioning(false)
    }, 300)
  }

  const goToPrevStep = () => {
    if (currentStep > 0) {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentStep(currentStep - 1)
        console.log('⬅️ Step voltou para:', currentStep - 1)
        setIsTransitioning(false)
      }, 200)
    }
  }

  // Atalho: avançar ao pressionar Enter nos campos de input
  const handleEnterKey = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      goToNextStep()
    }
  }

  // Handlers para dados pessoais
  const handleDadosPessoaisChange = (field: string, value: string) => {
    console.log('📝 Dados pessoais:', field, value)
    setDadosPessoais({ [field]: value })
    setErrors({})
  }

  // Handlers para perguntas
  const handleRadioChange = (perguntaId: string, opcaoId: string, valor: number) => {
    console.log('🎯 RADIO selecionado:', { perguntaId, opcaoId, valor })
    setRespostaRadio(perguntaId, opcaoId, valor)
    setErrors({})
    
    // Auto-avançar após selecionar (com delay para mostrar animação)
    setTimeout(() => {
      goToNextStep()
    }, 300)
  }

  const handleNumberChange = (perguntaId: string, valor: number) => {
    console.log('🔢 NUMBER alterado:', { perguntaId, valor })
    setRespostaNumber(perguntaId, valor)
    setErrors({})
  }

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <PingIcon />
      </div>
    )
  }

  if (result) {
    return (
      <ResultScreen
        result={result}
        nomeMarca={nomeMarca}
        offline={offline}
        onNewConsultation={resetQuiz}
      />
    )
  }


  const currentStepData = steps[currentStep]
  const progress = ((currentStep + 1) / steps.length) * 100
  // Detectar se a pergunta atual é de margem de lucro (para exibir hint e layout específico)
  const perguntaAtual = currentStepData?.type === 'question' && currentStepData.perguntaId
    ? perguntas.find(p => p.id === currentStepData.perguntaId)
    : undefined
  const cfgAtual: any = (perguntaAtual as any)?.config || {}
  const isMargem = Boolean(perguntaAtual && (cfgAtual.role === 'MARGEM_LUCRO' || perguntaAtual.ordem === 9))

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>


        {/* Transição Loading */}
        {isTransitioning && (
          <div className={styles.transitionOverlay}>
            <PingIcon />
          </div>
        )}

        {/* Content */}
        <div className={styles.content}>
          {currentStepData && (
            <>
              {/* Header da pergunta */}
              <div className={styles.header}>
                <h1 className={styles.title}>{currentStepData.title}</h1>
                {isMargem && (
                  <div className={styles.multiHintMargem }>
                    O percentual que sobra para a empresa depois de descontar todos os custos e despesas.
                  </div>
                )}
              </div>

              {/* Conteúdo do step */}
              <div className={styles.stepContent}>
                {currentStepData.type === "personal" && (
                  <div className={styles.inputContainer}>
                    {/* Nome da Marca */}
                    {currentStepData.fields?.includes("nomeMarca") && (
                      <FormattedInput
                        placeholder="Digite o nome da sua marca"
                        formatType="text"
                        value={nomeMarca}
                        autoFocus
                        onChange={(value) =>
                          handleDadosPessoaisChange(
                            "nomeMarca",
                            value as string
                          )
                        }
                        onKeyDown={handleEnterKey}
                        error={errors.nomeMarca}
                      />
                    )}

                    {/* Nome da Pessoa */}
                    {currentStepData.fields?.includes("nome") && (
                      <FormattedInput
                        placeholder="Digite seu nome completo"
                        formatType="text"
                        value={nome}
                        autoFocus
                        onChange={(value) =>
                          handleDadosPessoaisChange("nome", value as string)
                        }
                        onKeyDown={handleEnterKey}
                        error={errors.nome}
                      />
                    )}

                    {/* WhatsApp */}
                    {currentStepData.fields?.includes("whatsapp") && (
                      <FormattedInput
                        placeholder="(11) 99999-9999"
                        formatType="phone"
                        value={whatsapp}
                        autoFocus
                        onChange={(value) =>
                          handleDadosPessoaisChange("whatsapp", value as string)
                        }
                        onKeyDown={handleEnterKey}
                        error={errors.whatsapp}
                      />
                    )}

                    {/* E-mail */}
                    {currentStepData.fields?.includes("email") && (
                      <FormattedInput
                        placeholder="<EMAIL>"
                        formatType="text"
                        value={email}
                        autoFocus
                        onChange={(value) =>
                          handleDadosPessoaisChange("email", value as string)
                        }
                        onKeyDown={handleEnterKey}
                        error={errors.email}
                      />
                    )}
                  </div>
                )}

                {currentStepData.type === "question" &&
                  currentStepData.perguntaId && (
                    <>
                      {(() => {
                        const pergunta = perguntas.find(
                          (p) => p.id === currentStepData.perguntaId
                        );
                        if (!pergunta) return null;

                        console.log("🎯 Renderizando pergunta:", {
                          ordem: pergunta.ordem,
                          tipo: pergunta.tipo,
                          id: pergunta.id,
                        });

                        // Renderização baseada no tipo da pergunta
                        if (pergunta.tipo === "NUMBER") {
                          // Determinar tipo de formatação baseado na config, com fallback por ordem
                          const cfg = (pergunta as any).config || {}
                          let formatType:
                            | "currency"
                            | "percentage"
                            | "followers" = (cfg.numberFormat as any) || "followers";

                          if (!cfg.numberFormat) {
                            if (pergunta.ordem === 7 || pergunta.ordem === 8) {
                              formatType = "currency";
                            } else if (pergunta.ordem === 9) {
                              formatType = "percentage";
                            } else if (pergunta.ordem === 12) {
                              formatType = "followers";
                            }
                          }

                          const resposta = getResposta(pergunta.id);
                          const valorAtual =
                            resposta?.tipo === "NUMBER"
                              ? resposta.valorNumerico
                              : undefined;

                          console.log("🔢 Campo NUMBER:", {
                            ordem: pergunta.ordem,
                            formatType,
                            valorAtual,
                          });

                          if (isMargem) {
                            return (
                              <div className={styles.inputContainer}>
                                <NumberSwipeInput
                                  value={valorAtual ?? 0}
                                  onChange={(v) => handleNumberChange(pergunta.id, v)}
                                  formatType={formatType}
                                  layout="inline"
                                  showInput={false}
                                />
                                {errors[pergunta.id] && (
                                  <div className={styles.errorContainer}>
                                    <p className={styles.errorText}>
                                      {errors[pergunta.id]}
                                    </p>
                                  </div>
                                )}
                              </div>
                            )
                          }

                          // Faturamentos e seguidores: input simples
                          return (
                            <div className={styles.inputContainer}>
                              <FormattedInput
                                placeholder={
                                  formatType === "currency"
                                    ? "R$ 0,00"
                                    : formatType === "percentage"
                                    ? "0%"
                                    : "0"
                                }
                                formatType={formatType}
                                value={typeof valorAtual === 'number' ? valorAtual : ''}
                                autoFocus
                                onChange={(value) =>
                                  handleNumberChange(
                                    pergunta.id,
                                    value as number
                                  )
                                }
                                onKeyDown={handleEnterKey}
                                error={errors[pergunta.id]}
                              />
                            </div>
                          );
                        }

                        // Pergunta 16: reverter para o formato anterior (somente opções)

                        // Renderização vertical com estrelas para RADIO / CHECKBOX
                        const resposta = getResposta(pergunta.id)
                        const opcaoSelecionada = resposta?.tipo === 'RADIO' ? resposta.opcaoRespostaId : undefined
                        const opcoesSelecionadas = resposta?.tipo === 'CHECKBOX' ? new Set(resposta.opcaoRespostaIds) : new Set<string>()

                        // Sem rolagem: exibir estrelas ao lado do texto (apenas para tipos que não são múltiplos)
                        const rawMax = Math.max(...pergunta.opcoes.map(o => o.valor))
                        const maxStars = Math.min(5, Math.max(1, rawMax))
                        if (pergunta.tipo === 'CHECKBOX') {
                          return (
                            <div className={styles.optionsContainer}>
                              <div className={styles.multiHint}>Selecione uma ou mais opções</div>
                              {pergunta.opcoes.map((opcao) => {
                                const selected = opcoesSelecionadas.has(opcao.id)
                                return (
                                  <OptionButton
                                    key={opcao.id}
                                    selected={selected}
                                    multiple
                                    clamp={3}
                                    onClick={() => setRespostaCheckbox(pergunta.id, opcao.id, opcao.valor, !selected)}
                                  >
                                    {opcao.label}
                                  </OptionButton>
                                )
                              })}

                              {errors[pergunta.id] && (
                                <div className={styles.errorContainer}>
                                  <p className={styles.errorText}>
                                    {errors[pergunta.id]}
                                  </p>
                                </div>
                              )}
                            </div>
                          )
                        }

                        return (
                          <div className={styles.optionsContainer}>
                            {pergunta.opcoes.map((opcao) => (
                              <OptionButton
                                key={opcao.id}
                                selected={opcaoSelecionada === opcao.id}
                                onClick={() => handleRadioChange(pergunta.id, opcao.id, opcao.valor)}
                                clamp={3}
                                stars={pergunta.ordem === 15 ? undefined : (opcao.valor > 0 ? opcao.valor : undefined)}
                                maxStars={maxStars}
                              >
                                {opcao.label}
                              </OptionButton>
                            ))}

                            {errors[pergunta.id] && (
                              <div className={styles.errorContainer}>
                                <p className={styles.errorText}>
                                  {errors[pergunta.id]}
                                </p>
                              </div>
                            )}
                          </div>
                        )
                      })()}
                    </>
                  )}
              </div>

              {/* Navigation */}
              <div className={styles.navigation}>
                {/* Botão principal (OK) */}
                {currentStepData.type === "personal" && (
                  <button
                    onClick={goToNextStep}
                    disabled={submitting}
                    className={styles.primaryButton}
                  >
                    <span className={styles.okText}>OK</span>
                    <Image
                      src="/icons/arrow-right.svg"
                      alt="Avançar"
                      width={26}
                      height={29}
                      className={styles.okIcon}
                      priority
                    />
                  </button>
                )}

                {currentStepData.type === "question" && (() => {
                  const q = perguntas.find((p) => p.id === currentStepData.perguntaId)
                  const respondeu = q ? !!getResposta(q.id) : false
                  const showOk = (q?.tipo === 'NUMBER' || q?.tipo === 'CHECKBOX') ? respondeu : q?.ordem === 16
                  return showOk
                })() && (
                  <button
                    onClick={goToNextStep}
                    disabled={submitting}
                    className={styles.primaryButton}
                  >
                    <span className={styles.okText}>OK</span>
                    <Image
                      src="/icons/arrow-right.svg"
                      alt="Avançar"
                      width={26}
                      height={29}
                      className={styles.okIcon}
                      priority
                    />
                  </button>
                )}

                {/* Botão voltar abaixo do OK */}
                {currentStep > 0 && (
                  <button
                    type="button"
                    onClick={goToPrevStep}
                    className={styles.backButton}
                  >
                    <Image
                      src="/icons/arrow-return.svg"
                      alt="Voltar"
                      width={47}
                      height={37}
                      className={styles.backIcon}
                      priority
                    />
                  </button>
                )}
              </div>
            </>
          )}
        </div>

        
      </div>
    </div>
  );
}