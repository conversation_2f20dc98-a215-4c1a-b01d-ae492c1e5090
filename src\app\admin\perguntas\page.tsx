'use client'

import { useEffect, useState } from 'react'
import { DndContext, closestCenter, DragEndEvent, useSensor, useSensors, PointerSensor, KeyboardSensor } from '@dnd-kit/core'
import { SortableContext, useSortable, verticalListSortingStrategy, arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Toaster, toast } from 'react-hot-toast'
import type { Pergunta } from '@/types'
import styles from './AdminPerguntas.module.css'

type EditingPergunta = Pergunta & { _localId?: string }

type OptionRowProps = {
  perguntaId: string
  opcao: any
  isProtected: boolean
  editEnabled: boolean
  onEnterEdit: () => void
  onChangeLabel: (value: string) => void
  onChangeValor: (value: number) => void
  onDelete: () => void
}

function OptionRow({
  perguntaId,
  opcao,
  isProtected,
  editEnabled,
  onEnterEdit,
  onChangeLabel,
  onChangeValor,
  onDelete
}: OptionRowProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: opcao.id, disabled: isProtected })
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
    cursor: isProtected ? 'not-allowed' : 'default'
  } as React.CSSProperties

  return (
    <div
      ref={setNodeRef}
      className={styles.optionRow}
      style={style}
      title={isProtected ? 'Protegido' : 'Arraste pelo ícone ☰; clique no input para editar'}
    >
      <div className={styles.dragHandle} aria-label="Arrastar" {...attributes} {...listeners}>
        ☰
      </div>
      <input
        className={styles.optionInput}
        value={opcao.label}
        readOnly={!editEnabled}
        onChange={e => onChangeLabel(e.target.value)}
        onFocus={onEnterEdit}
        style={{ pointerEvents: 'auto' }}
        aria-disabled={isProtected || !editEnabled}
      />
      <input
        type="number"
        className={styles.optionNumber}
        value={opcao.valor}
        readOnly={!editEnabled}
        onChange={e => onChangeValor(Number(e.target.value))}
        onFocus={onEnterEdit}
        style={{ pointerEvents: 'auto' }}
        aria-disabled={isProtected || !editEnabled}
      />
      <button
        className={styles.optionDelete}
        disabled={isProtected}
        onClick={isProtected ? undefined : onDelete}
      >
        X
      </button>
    </div>
  )
}

export default function AdminPerguntasPage() {
  const [perguntas, setPerguntas] = useState<EditingPergunta[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pinOk, setPinOk] = useState(false)
  const [pin, setPin] = useState('')
  const [originalPerguntas, setOriginalPerguntas] = useState<EditingPergunta[]>([])
  const [editRowByPergunta, setEditRowByPergunta] = useState<Record<string, string | null>>({})

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates })
  )

  const protectedRoles = new Set(['FATURAMENTO_2025','FATURAMENTO_2026','MARGEM_LUCRO','SEGUIDORES'])
  const protectedOrdensPrecisos = new Set([7,8,9,12])
  const isProtected = (p: EditingPergunta) => {
    const role = (p.config as any)?.role
    return protectedOrdensPrecisos.has(p.ordem) || (role && protectedRoles.has(role))
  }

  const handleDragEnd = (p: EditingPergunta) => (event: DragEndEvent) => {
    const { active, over } = event
    if (!over || active.id === over.id) return
    const ids = (p.opcoes || []).map(o => o.id)
    const oldIndex = ids.indexOf(String(active.id))
    const newIndex = ids.indexOf(String(over.id))
    if (oldIndex < 0 || newIndex < 0) return
    setPerguntas(prev => prev.map(x => {
      if (x.id !== p.id) return x
      const arr = arrayMove([...(x.opcoes || [])], oldIndex, newIndex)
      return { ...x, opcoes: arr }
    }))
  }
  const showProtectedMsg = () => toast("🛑 Eita! Essa pergunta impacta o cálculo. Fale com o dev 😉")

  const load = async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await fetch('/api/admin/perguntas', { cache: 'no-store' })
      const data = await res.json()
      setPerguntas(data)
      setOriginalPerguntas(JSON.parse(JSON.stringify(data)))
    } catch (e: any) {
      setError('Falha ao carregar perguntas')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const check = async () => {
      const res = await fetch('/api/admin/me', { cache: 'no-store' })
      const data = await res.json()
      if (data?.authenticated) {
        setPinOk(true)
        await load()
      } else {
        setLoading(false)
      }
    }
    check()
  }, [])

  const doLogin = async () => {
    try {
      const res = await fetch('/api/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pin })
      })
      if (res.ok) {
        setPin('')
        setPinOk(true)
        await load()
      } else {
        toast.error('PIN inválido')
      }
    } catch (e) {
      toast.error('Falha no login')
    }
  }

  const handleAdd = () => {
    const novo: EditingPergunta = {
      id: `local-${Math.random().toString(36).slice(2)}`,
      texto: 'Nova pergunta',
      tipo: 'RADIO',
      multiplicador: false,
      ordem: (perguntas[perguntas.length - 1]?.ordem ?? 0) + 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      opcoes: [],
      config: {}
    }
    setPerguntas(prev => [...prev, novo])
  }

  const handleSave = async (p: EditingPergunta) => {
    setSaving(true)
    setError(null)
    try {
      const isLocal = p.id.startsWith('local-')

      const findOriginal = (id: string) => originalPerguntas.find(op => op.id === id)
      const original = findOriginal(p.id)
      const haveLocalOptions = (p.opcoes || []).some(o => o.id.startsWith('local-'))

      const haveOptionsChanged = () => {
        if (!original) return true
        const curr = p.opcoes || []
        const prev = original.opcoes || []
        if (curr.length !== prev.length) return true
        // Map by id for label/valor comparison
        const prevById = new Map(prev.map(o => [o.id, o]))
        for (const o of curr) {
          const po = prevById.get(o.id)
          if (!po) return true
          if (po.label !== o.label) return true
          if (po.valor !== o.valor) return true
        }
        // If only reorder changed, return false
        return false
      }

      // Decide whether to send full options or only ordering
      const shouldSendFullOptions = isLocal || haveLocalOptions || haveOptionsChanged()

      const basePayload: any = {
        texto: p.texto,
        tipo: p.tipo,
        multiplicador: p.multiplicador,
        ordem: p.ordem,
        config: p.config ?? {}
      }

      const payload: any = shouldSendFullOptions
        ? { ...basePayload, opcoes: (p.opcoes || []).map(o => ({ label: o.label, valor: o.valor })) }
        : { ...basePayload, opcoesOrder: (p.opcoes || []).map(o => o.id) }

      const res = await fetch(isLocal ? '/api/admin/perguntas' : `/api/admin/perguntas/${p.id}`, {
        method: isLocal ? 'POST' : 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
      if (!res.ok) throw new Error('Falha ao salvar')
      await load()
    } catch (e: any) {
      setError(e.message || 'Erro ao salvar')
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (id.startsWith('local-')) {
      setPerguntas(prev => prev.filter(x => x.id !== id))
      return
    }
    setSaving(true)
    setError(null)
    try {
      const res = await fetch(`/api/admin/perguntas/${id}`, { method: 'DELETE' })
      if (!res.ok) throw new Error('Falha ao deletar')
      await load()
    } catch (e: any) {
      setError(e.message || 'Erro ao deletar')
    } finally {
      setSaving(false)
    }
  }

  if (!pinOk) {
    return (
      <div className={styles.page}>
        <div className={styles.container}>
          <div className={styles.card}>
            <h1 className={styles.title}>Acesso restrito</h1>
            <p className={styles.label}>Digite o PIN para acessar a gestão de perguntas.</p>
            <input
              type="password"
              className={styles.input}
              placeholder="PIN"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              onKeyDown={(e) => { if (e.key === 'Enter') doLogin() }}
            />
            <div className={styles.buttons}>
              <button className={`${styles.button} ${styles.save}`} onClick={doLogin}>Entrar</button>
              <a href="/" className={`${styles.button}`}>Cancelar</a>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (loading) return <div className={styles.page}><div className={styles.container}>Carregando...</div></div>

  return (
    <div className={styles.page}>
      <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Gestão do Formulário</h1>
        <div className={styles.actions}>
          <button className={styles.addButton} onClick={handleAdd}>Adicionar Pergunta</button>
        </div>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      <div className={styles.list}>
        {perguntas.map((p) => (
          <div key={p.id} className={styles.card}>
            <div className={styles.row}>
              <div className={styles.field}>
                <label className={styles.label}>Texto</label>
                <input
                  className={styles.input}
                  value={p.texto}
                  disabled={false}
                  onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, texto: e.target.value } : x))}
                />
              </div>
              <div className={styles.field}>
                <label className={styles.label}>Tipo</label>
                <select
                  className={styles.select}
                  value={p.tipo}
                  disabled={isProtected(p)}
                  onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, tipo: e.target.value } : x))}
                >
                  <option value="RADIO">RADIO</option>
                  <option value="CHECKBOX">CHECKBOX</option>
                  <option value="NUMBER">NUMBER</option>
                  <option value="TEXT">TEXT</option>
                  <option value="EMAIL">EMAIL</option>
                  <option value="PHONE">PHONE</option>
                </select>
              </div>
              <div className={styles.field}>
                <label className={styles.label}>Ordem</label>
                <input
                  type="number"
                  className={styles.input}
                  value={p.ordem}
                  disabled={isProtected(p)}
                  onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, ordem: Number(e.target.value) } : x))}
                />
              </div>
              <div className={styles.buttons}>
                <button
                  className={`${styles.button} ${styles.delete}`}
                  onClick={() => isProtected(p) ? showProtectedMsg() : handleDelete(p.id)}
                  disabled={isProtected(p)}
                >
                  Excluir
                </button>
                <button
                  className={`${styles.button} ${styles.save}`}
                  onClick={() => handleSave(p)}
                  disabled={saving}
                >
                  {saving ? 'Salvando...' : 'Salvar'}
                </button>
              </div>
            </div>

            {isProtected(p) && (
              <div className={styles.note}>
                ⚠️ Pergunta crítica para o cálculo. Você pode editar o texto, mas não a ordem, tipo, role, opções ou excluir.
              </div>
            )}

            {p.tipo === 'NUMBER' && (
              <div className={styles.subGrid}>
                <div className={styles.field}>
                  <label className={styles.label}>Formato Numérico</label>
                  <select
                    className={styles.select}
                    value={(p.config as any)?.numberFormat ?? ''}
                    disabled={isProtected(p)}
                    onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, config: { ...(x.config ?? {}), numberFormat: e.target.value || undefined } } : x))}
                  >
                    <option value="">Auto (por ordem)</option>
                    <option value="currency">Moeda (R$)</option>
                    <option value="percentage">Percentual (%)</option>
                    <option value="followers">Seguidores</option>
                  </select>
                </div>
                <div className={styles.field}>
                  <label className={styles.label}>Papel (Role)</label>
                  <select
                    className={styles.select}
                    value={(p.config as any)?.role ?? ''}
                    disabled={isProtected(p)}
                    onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, config: { ...(x.config ?? {}), role: e.target.value || undefined } } : x))}
                  >
                    <option value="">Nenhum</option>
                    <option value="FATURAMENTO_2025">Faturamento 2025</option>
                    <option value="FATURAMENTO_2026">Faturamento 2026</option>
                    <option value="MARGEM_LUCRO">Margem de Lucro (%)</option>
                    <option value="SEGUIDORES">Seguidores Totais</option>
                  </select>
                </div>
                <div className={styles.field}>
                  <label className={styles.label}>Pontua?</label>
                  <div className={styles.checkboxWrap}>
                    <input
                      type="checkbox"
                      className={styles.checkbox}
                      checked={Boolean((p.config as any)?.pontua ?? (p.tipo === 'RADIO' as string))}
                      disabled={isProtected(p)}
                      onChange={e => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, config: { ...(x.config ?? {}), pontua: e.target.checked } } : x))}
                    />
                  </div>
                </div>
              </div>
            )}

            {['RADIO', 'CHECKBOX'].includes(p.tipo) && (
              <div className={styles.options}>
                <div className={styles.optionsHeader}>
                  <h3 className={styles.sectionTitle}>Opções</h3>
                  <button
                    className={styles.addOption}
                    disabled={isProtected(p)}
                    onClick={() => isProtected(p)
                      ? showProtectedMsg()
                      : setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, opcoes: [...(x.opcoes || []), { id: `local-${Math.random().toString(36).slice(2)}`, label: 'Opção', valor: (x.opcoes?.length || 0) + 1, perguntaId: p.id, createdAt: new Date() }] } : x))}
                  >
                    Adicionar Opção
                  </button>
                </div>
                <div className={styles.list}>
                  <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd(p)}>
                    <SortableContext items={(p.opcoes || []).map(o => o.id)} strategy={verticalListSortingStrategy}>
                      {p.opcoes?.map((o, i) => (
                        <OptionRow
                          key={o.id}
                          perguntaId={p.id}
                          opcao={o}
                          isProtected={isProtected(p)}
                          editEnabled={Boolean(editRowByPergunta[p.id] === o.id)}
                          onEnterEdit={() => {
                            if (isProtected(p)) return showProtectedMsg()
                            setEditRowByPergunta(prev => ({ ...prev, [p.id]: o.id }))
                          }}
                          onChangeLabel={(value) => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, opcoes: x.opcoes.map((y, yi) => yi === i ? { ...y, label: value } : y) } : x))}
                          onChangeValor={(value) => setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, opcoes: x.opcoes.map((y, yi) => yi === i ? { ...y, valor: value } : y) } : x))}
                          onDelete={() => isProtected(p)
                            ? showProtectedMsg()
                            : setPerguntas(prev => prev.map(x => x.id === p.id ? { ...x, opcoes: x.opcoes.filter((_, yi) => yi !== i) } : x))}
                        />
                      ))}
                    </SortableContext>
                  </DndContext>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      </div>
      <Toaster position="top-center" />
    </div>
  )
}
