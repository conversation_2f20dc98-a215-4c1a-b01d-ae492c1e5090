"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { OfflineService, IndexedDBTester } from '@/lib/indexeddb'
import { SyncManager } from '@/lib/sync-manager'
import { useServiceWorker } from '@/hooks/useServiceWorker'
import { useOfflineStatus } from '@/hooks/useOfflineStatus'
import type { Pergunta, FormularioLead } from '@/types'
import { useQuizStore } from '@/stores/quizStore'
import { MarcaCalculator } from '@/lib/calculations'

interface QuizResult {
  leadId: string
  resultado: {
    pontuacaoTotal: number
    percentualFator: number
    faturamento2025: number
    faturamento2026: number
    margemLucro: number
    lucroTotal10Anos: number
    valorEstimado: number
    valorFormatado: string
    percentualFormatado: string
    resumoCalculo: string
    projecaoAnual: number[]
  }
}

export function useQuizForm(options?: { offline?: boolean }) {
  const offlineMode = options?.offline ?? true
  const form = useForm<any>({ mode: 'onChange' })
  const [perguntas, setPerguntas] = useState<Pergunta[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [result, setResult] = useState<QuizResult | null>(null)
  
  // Hooks para funcionalidade offline
  const { isOnline, wasOffline } = useOfflineStatus()
  const { triggerBackgroundSync } = useServiceWorker()

  // Zustand store
  const { resetQuiz } = useQuizStore()

  // Auto-sync quando voltar online (apenas no modo offline/PWA)
  useEffect(() => {
    if (!offlineMode) return
    if (isOnline && wasOffline) {
      console.log('🔄 Reconectado! Iniciando sync automática...')
      SyncManager.fullSync()
    }
  }, [isOnline, wasOffline, offlineMode])

  // Carregar perguntas
  useEffect(() => {
    async function loadQuestions() {
      try {
        if (!offlineMode) {
          // Modo visitante (online-only): buscar direto da API
          const response = await fetch('/api/perguntas', { cache: 'no-store' })
          if (!response.ok) throw new Error('Falha ao carregar perguntas')
          const perguntasApi = await response.json()
          setPerguntas(perguntasApi)
          return
        }

        // Modo offline/PWA
        console.log('🧪 Testando IndexedDB na inicialização...')
        const dbWorking = await IndexedDBTester.testConnection()
        if (!dbWorking) {
          toast.error('Erro: IndexedDB não está funcionando!')
        }

        // Tentar carregar offline primeiro
        const offlineQuestions = await OfflineService.getQuestionsOffline()
        if (offlineQuestions.length > 0) {
          const perguntasConvertidas: Pergunta[] = offlineQuestions.map(q => ({
            ...q,
            // garantir campo config
            config: (q as any).config ?? undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            opcoes: q.opcoes.map(opcao => ({
              ...opcao,
              perguntaId: q.id,
              createdAt: new Date()
            }))
          }))
          setPerguntas(perguntasConvertidas)
          console.log('📋 Perguntas carregadas do cache:', perguntasConvertidas.length)
        }

        // Sincronizar do servidor em background se online
        if (isOnline) {
          const synced = await SyncManager.syncQuestions()
          if (synced) {
            const updated = await OfflineService.getQuestionsOffline()
            if (updated.length > 0) {
              const convert: Pergunta[] = updated.map(q => ({
                ...q,
                config: (q as any).config ?? undefined,
                createdAt: new Date(),
                updatedAt: new Date(),
                opcoes: q.opcoes.map(opcao => ({
                  ...opcao,
                  perguntaId: q.id,
                  createdAt: new Date()
                }))
              }))
              setPerguntas(convert)
              console.log('🔄 Perguntas atualizadas do servidor:', convert.length)
            }
          }
        } else if (offlineQuestions.length === 0) {
          toast.error('Nenhuma pergunta disponível offline')
        }
      } catch (error) {
        console.error('❌ Erro ao carregar perguntas:', error)
        toast.error('Erro ao carregar perguntas')
      } finally {
        setLoading(false)
      }
    }

    loadQuestions()
  }, [isOnline, offlineMode])

  // Submeter formulário
  const onSubmit = async (data?: FormularioLead) => {
    // Se data não foi passado, usar dados da store
    if (!data) {
      const { getAllDados } = useQuizStore.getState()
      const storeData = getAllDados()

      // Converter dados da store para formato esperado pelo onSubmit
      data = {
        nomeMarca: storeData.nomeMarca,
        nome: storeData.nome,
        whatsapp: storeData.whatsapp,
        email: storeData.email,
        respostas: {}
      }

      // Converter respostas da store para formato esperado, incluindo CHECKBOX
      Object.entries(storeData.respostas).forEach(([perguntaId, resposta]) => {
        if (resposta.tipo === 'RADIO') {
          data!.respostas[perguntaId] = resposta.opcaoRespostaId
        } else if (resposta.tipo === 'NUMBER') {
          data!.respostas[perguntaId] = resposta.valorNumerico
        } else if (resposta.tipo === 'CHECKBOX') {
          data!.respostas[perguntaId] = resposta.opcaoRespostaIds
        }
      })
    }

    setSubmitting(true)

    try {
      // Validar se todas as perguntas foram respondidas
      const perguntasNaoRespondidas = perguntas.filter(
        p => !data!.respostas[p.id]
      )

      console.log('🔍 DEBUG Validação de perguntas:', {
        totalPerguntas: perguntas.length,
        respostasEncontradas: Object.keys(data!.respostas).length,
        perguntasNaoRespondidas: perguntasNaoRespondidas.length,
        perguntasNaoRespondidasDetalhes: perguntasNaoRespondidas.map(p => ({
          id: p.id,
          ordem: p.ordem,
          tipo: p.tipo,
          texto: p.texto,
          temOpcoes: p.opcoes.length > 0
        }))
      })

      if (perguntasNaoRespondidas.length > 0) {
        console.log('❌ Perguntas não respondidas:', perguntasNaoRespondidas.map(p => `${p.ordem}: ${p.texto}`))
        toast.error('Por favor, responda todas as perguntas')
        setSubmitting(false)
        return
      }

      console.log('🚀 Iniciando submissão do formulário...')
      console.log('📊 Dados para submissão:', data)

      // Preparar respostas para cálculo com nova estrutura
      const respostasCalculo = Object.entries(data!.respostas).map(([perguntaId, valorResposta]) => {
        const pergunta = perguntas.find(p => p.id === perguntaId)
        
        if (!pergunta) {
          console.warn(`Pergunta não encontrada: ${perguntaId}`)
          return null
        }

        // Para perguntas NUMBER (7, 8, 9, 12)
        if (pergunta.tipo === 'NUMBER') {
          const valorNumerico = typeof valorResposta === 'number' ? valorResposta : parseFloat(valorResposta as string)
          
          return {
            perguntaId,
            valorNumerico,
            ordem: pergunta.ordem,
            role: (pergunta as any).config?.role
          }
        }
        
        // Para perguntas RADIO (5, 6, 10-16)
        const opcao = pergunta.opcoes.find(o => o.id === valorResposta)
        const valor = opcao?.valor || 0
        
        return {
          perguntaId,
          valor,
          ordem: pergunta.ordem,
          role: (pergunta as any).config?.role
        }
      }).filter(Boolean) as Array<{
        perguntaId: string
        valor?: number
        valorNumerico?: number
        ordem: number
        role?: 'FATURAMENTO_2025' | 'FATURAMENTO_2026' | 'MARGEM_LUCRO' | 'SEGUIDORES'
      }>

      // Calcular resultado usando a lógica existente
      const resultado = MarcaCalculator.calcularValorMarca(respostasCalculo)
      console.log('🧮 Resultado calculado:', resultado)

      let leadOfflineId: number | undefined
      // Gerar clientId para idempotência
      const clientId = crypto?.randomUUID ? crypto.randomUUID() : `client-${Date.now()}-${Math.random()}`
      if (offlineMode) {
        // Converter respostas e salvar offline
        const respostasOffline = Object.entries(data!.respostas).map(([perguntaId, valorResposta]) => {
          const pergunta = perguntas.find(p => p.id === perguntaId)
          if (pergunta?.tipo === 'NUMBER') {
            return {
              perguntaId,
              valorNumerico: typeof valorResposta === 'number' ? valorResposta : parseFloat(valorResposta as string)
            }
          }
          if (pergunta?.tipo === 'CHECKBOX') {
            const ids = Array.isArray(valorResposta) ? valorResposta as string[] : [String(valorResposta)]
            return { perguntaId, opcaoRespostaIds: ids }
          }
          return { perguntaId, opcaoRespostaId: valorResposta as string }
        })
        console.log('💾 Salvando lead offline...')
        leadOfflineId = await OfflineService.saveLeadOffline({
          clientId,
          nomeMarca: data!.nomeMarca,
          nome: data!.nome,
          whatsapp: data!.whatsapp,
          email: data!.email,
          respostas: respostasOffline,
          pontuacaoTotal: resultado.pontuacaoTotal,
          percentualFator: resultado.percentualFator,
          valorEstimado: resultado.valorEstimado,
          faturamento2025: resultado.faturamento2025,
          faturamento2026: resultado.faturamento2026,
          margemLucro: resultado.margemLucro,
          lucroTotal10Anos: resultado.lucroTotal10Anos,
          projecaoAnual: resultado.projecaoAnual
        })
        console.log('✅ Lead salvo offline com ID:', leadOfflineId)
      }

      // Preparar resultado para exibição com nova estrutura
      const resultadoExibicao = {
        leadId: leadOfflineId ? `offline-${leadOfflineId}` : 'offline-' + Date.now(),
        resultado: {
          pontuacaoTotal: resultado.pontuacaoTotal,
          percentualFator: resultado.percentualFator,
          faturamento2025: resultado.faturamento2025,
          faturamento2026: resultado.faturamento2026,
          margemLucro: resultado.margemLucro,
          lucroTotal10Anos: resultado.lucroTotal10Anos,
          valorEstimado: resultado.valorEstimado,
          valorFormatado: MarcaCalculator.formatarMoeda(resultado.valorEstimado),
          percentualFormatado: MarcaCalculator.formatarPercentual(resultado.percentualFator),
          resumoCalculo: MarcaCalculator.obterResumoCalculo(resultado),
          projecaoAnual: resultado.projecaoAnual
        }
      }

      setResult(resultadoExibicao)

      if (isOnline) {
        console.log('🌐 Online - tentando sincronizar imediatamente...')
        try {
          // Tentar enviar para servidor também
          const response = await fetch('/api/leads', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ...data, clientId })
          })

          if (response.ok) {
            console.log('✅ Lead sincronizado com servidor!')
            toast.success('Avaliação concluída e sincronizada!')
            
            // Marcar como sincronizado se foi salvo com sucesso (somente no modo offline)
            if (offlineMode && leadOfflineId) await OfflineService.markLeadAsSynced(leadOfflineId)
          } else {
            if (offlineMode) {
              console.log('⚠️ Servidor indisponível, dados salvos localmente')
              toast.success('Dados salvos localmente! Serão sincronizados automaticamente.')
            } else {
              toast.error('Falha ao enviar. Tente novamente.')
            }
          }
        } catch (error) {
          if (offlineMode) {
            console.log('⚠️ Erro de rede, dados salvos localmente:', error)
            toast.success('Dados salvos localmente! Serão sincronizados quando houver conexão.')
          } else {
            toast.error('Sem conexão. Tente novamente em instantes.')
          }
        }
      } else {
        if (offlineMode) {
          console.log('📴 Offline - dados salvos localmente')
          toast.success('Dados salvos localmente! Serão sincronizados quando voltar online.')
        } else {
          toast.error('Você está offline. Não foi possível enviar.')
        }
      }

      // Registrar background sync se disponível (somente no modo offline)
      if (offlineMode && triggerBackgroundSync) {
        console.log('🔄 Registrando background sync...')
        try {
          triggerBackgroundSync('sync-leads')
        } catch (error) {
          console.log('⚠️ Background sync não disponível:', error)
        }
      }

      // ✅ Submit concluído com sucesso - formulário permanece limpo
      console.log('✅ Submit concluído - formulário limpo e pronto para nova consulta')

    } catch (error) {
      console.error('❌ Erro ao submeter formulário:', error)
      toast.error('Erro ao processar avaliação. Tente novamente.')
    } finally {
      setSubmitting(false)
    }
  }

  // Reset do quiz (mantido para compatibilidade)
  const resetQuizForm = () => {
    console.log('🔄 Resetando quiz...')
    resetQuiz()
    setResult(null)
    setSubmitting(false)
  }

  // Sincronizar dados
  const syncData = async () => {
    console.log('🔄 Iniciando sincronização manual...')
    const result = await SyncManager.fullSync()
    console.log('📊 Resultado da sincronização:', result)
    return result
  }

  // Submissão manual (mantido para compatibilidade)
  const submitManually = async () => {
    console.log('🔍 DEBUG submitManually - iniciando...')
    await onSubmit()
  }

  return {
    perguntas,
    loading,
    submitting,
    result,
    onSubmit,
    resetQuiz: resetQuizForm,
    syncData,
    submitManually,
    isOnline,
    // compatibilidade com QuizForm antigo
    isOffline: !isOnline,
    form
  }
}