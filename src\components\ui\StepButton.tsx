"use client"

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface StepButtonProps {
  children: React.ReactNode
  selected?: boolean
  onClick?: () => void
  className?: string
  disabled?: boolean
}

const StepButton = forwardRef<HTMLButtonElement, StepButtonProps>(
  ({ children, selected = false, onClick, className, disabled = false }, ref) => {
    return (
      <button
        ref={ref}
        onClick={onClick}
        disabled={disabled}
        className={cn(
          // Base styles
          'w-full p-6 rounded-2xl text-left font-medium transition-all duration-300',
          'border-2 text-gray-700 shadow-sm',
          'hover:shadow-md transform hover:scale-[1.02]',
          'focus:outline-none focus:ring-4 focus:ring-[#269AB6]/20',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          
          // Selected state
          selected
            ? 'border-[#269AB6] bg-[#269AB6] text-white shadow-lg scale-[1.02] opacity-95'
            : 'border-gray-300 bg-white hover:border-[#269AB6]/40 text-gray-800',
          
          // Click animation
          'active:scale-[0.98] active:opacity-75',
          
          className
        )}
      >
        <div className="flex items-center justify-between">
          <span className="text-base leading-relaxed pr-4">
            {children}
          </span>
          {selected && (
            <div className="flex-shrink-0">
              <svg 
                className="w-6 h-6 text-white" 
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path 
                  fillRule="evenodd" 
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                  clipRule="evenodd" 
                />
              </svg>
            </div>
          )}
        </div>
      </button>
    )
  }
)

StepButton.displayName = 'StepButton'

export { StepButton }