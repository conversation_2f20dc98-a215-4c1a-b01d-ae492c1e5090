// Otimizações de performance para PWA

/**
 * Lazy loading de componentes com preload opcional
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  preload = false
) {
  const LazyComponent = React.lazy(importFunc)
  
  if (preload && typeof window !== 'undefined') {
    // Preload no próximo tick
    setTimeout(importFunc, 0)
  }
  
  return LazyComponent
}

/**
 * Hook para detectar intersecção (lazy loading de seções)
 */
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = { threshold: 0.1 }
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false)
  
  React.useEffect(() => {
    const element = ref.current
    if (!element) return
    
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
    }, options)
    
    observer.observe(element)
    
    return () => {
      observer.unobserve(element)
    }
  }, [ref, options])
  
  return isIntersecting
}

/**
 * Hook para preload de recursos críticos
 */
export function useResourcePreload() {
  React.useEffect(() => {
    // Preload critical resources
    const criticalResources = [
      '/api/perguntas',
      '/icons/icon-192x192.png',
      '/manifest.json'
    ]
    
    criticalResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      
      if (resource.startsWith('/api/')) {
        link.as = 'fetch'
        link.crossOrigin = 'anonymous'
      } else if (resource.endsWith('.png') || resource.endsWith('.jpg')) {
        link.as = 'image'
      } else if (resource.endsWith('.json')) {
        link.as = 'fetch'
        link.crossOrigin = 'anonymous'
      }
      
      link.href = resource
      document.head.appendChild(link)
    })
  }, [])
}

/**
 * Debounce para otimizar inputs
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Performance monitoring
 */
export class PerformanceMonitor {
  private static marks: Map<string, number> = new Map()
  
  static mark(name: string) {
    if ('performance' in window && performance.mark) {
      performance.mark(name)
      this.marks.set(name, Date.now())
    }
  }
  
  static measure(name: string, startMark: string, endMark?: string) {
    if ('performance' in window && performance.measure) {
      try {
        if (endMark) {
          performance.measure(name, startMark, endMark)
        } else {
          performance.measure(name, startMark)
        }
        
        const entries = performance.getEntriesByName(name, 'measure')
        const latestEntry = entries[entries.length - 1]
        
        console.log(`⚡ Performance: ${name} took ${latestEntry.duration.toFixed(2)}ms`)
        
        return latestEntry.duration
      } catch (error) {
        console.warn('Performance measurement failed:', error)
      }
    }
    
    return null
  }
  
  static getPageLoadMetrics() {
    if ('performance' in window && performance.getEntriesByType) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      return {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        ssl: navigation.secureConnectionStart > 0 ? navigation.connectEnd - navigation.secureConnectionStart : 0,
        ttfb: navigation.responseStart - navigation.requestStart,
        download: navigation.responseEnd - navigation.responseStart,
        domParse: navigation.domContentLoadedEventStart - navigation.responseEnd,
        domReady: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - (navigation as any).navigationStart
      }
    }
    
    return null
  }
}

/**
 * Cache para requests API
 */
export class RequestCache {
  private static cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map()
  
  static set(key: string, data: any, ttlMinutes: number = 10) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000
    })
  }
  
  static get(key: string) {
    const cached = this.cache.get(key)
    
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }
  
  static clear() {
    this.cache.clear()
  }
}

/**
 * React import fix
 */
import React from 'react'