"use client"

import { useState, useEffect } from 'react'

export function useOfflineStatus() {
  const [isOnline, setIsOnline] = useState(true)
  const [wasOffline, setWasOffline] = useState(false)

  useEffect(() => {
    // Verificar se estamos no cliente
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      // Estado inicial
      setIsOnline(navigator.onLine)

      const handleOnline = () => {
        setIsOnline(true)
        if (wasOffline) {
          console.log('Conexão restaurada')
          setWasOffline(false)
        }
      }

      const handleOffline = () => {
        setIsOnline(false)
        setWasOffline(true)
        console.log('Conexão perdida')
      }

      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)

      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
      }
    }
  }, [wasOffline])

  return {
    isOnline,
    wasOffline
  }
}