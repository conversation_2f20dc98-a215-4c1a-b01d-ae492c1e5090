import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

function base64url(input: Buffer | string) {
  return Buffer.from(input)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
}

function sign(payload: object, secret: string) {
  const header = { alg: 'HS256', typ: 'JWT' }
  const encHeader = base64url(JSON.stringify(header))
  const encPayload = base64url(JSON.stringify(payload))
  const data = `${encHeader}.${encPayload}`
  const signature = crypto.createHmac('sha256', secret).update(data).digest()
  const encSignature = base64url(signature)
  return `${data}.${encSignature}`
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const pin = String(body?.pin || '')

    const expectedPin = process.env.ADMIN_PIN || process.env.KIOSK_PIN
    if (!pin || !expectedPin || pin !== expectedPin) {
      return NextResponse.json({ error: 'PIN inválido' }, { status: 401 })
    }

    const secret = process.env.ADMIN_JWT_SECRET || process.env.KIOSK_JWT_SECRET || 'change-me-secret'
    const token = sign({ role: 'admin', iat: Date.now() }, secret)

    const res = NextResponse.json({ ok: true })
    res.cookies.set('admin_auth', token, {
      httpOnly: true,
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
      path: '/',
      maxAge: 60 * 60 * 24,
    })
    return res
  } catch (error) {
    return NextResponse.json({ error: 'Bad Request' }, { status: 400 })
  }
}


