/**
 * Tabela de Pontuação x Percentual para Valuation de Marca
 * Baseada na regra de negócio oficial
 */
export const pontuacaoFatores: Record<number, number> = {
  1: 0.030,
  2: 0.037,
  3: 0.044,
  4: 0.051,
  5: 0.058,
  6: 0.064,
  7: 0.071,
  8: 0.078,
  9: 0.085,
  10: 0.092,
  11: 0.099,
  12: 0.106,
  13: 0.113,
  14: 0.119,
  15: 0.126,
  16: 0.133,
  17: 0.140,
  18: 0.147,
  19: 0.154,
  20: 0.161,
  21: 0.168,
  22: 0.174,
  23: 0.181,
  24: 0.188,
  25: 0.195,
  26: 0.202,
  27: 0.209,
  28: 0.216,
  29: 0.223,
  30: 0.229,
  31: 0.236,
  32: 0.243,
  33: 0.250,
};

/**
 * Busca o percentual correspondente à pontuação
 * Aplica teto de 33 pontos conforme regra de negócio
 */
export function obterPercentualPorPontuacao(pontuacao: number): number {
  // Aplicar teto de 33 pontos
  const pontuacaoLimitada = Math.min(Math.floor(pontuacao), 33);
  
  // Retornar percentual da tabela
  return pontuacaoFatores[pontuacaoLimitada] || 0;
}