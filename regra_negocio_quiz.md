# 📄 Contexto de Valuation de Marca

Este documento descreve a **regra de negócio completa** do quiz de **valuation de marca**, incluindo:

- Perguntas e opções de resposta  
- Pontuação e cálculo de seguidores  
- Fórmulas para faturamento e lucro projetado  
- Cálculo final do **valor da marca**  
- Tabela de **pontuação x percentual**  
- Objeto TypeScript para uso no front-end  

---

## 1️⃣ Estrutura do Quiz

### Perguntas coletadas diretamente do lead (não pontuam)
1. Nome da Marca  
2. Nome  
3. WhatsApp  
4. Email  

### Perguntas 5 e 6 (não pontuam)
5. **Qual das alternativas abaixo melhor define o seu negócio?**  
   - Vestuário  
   - Fabricação de produtos  
   - Comércio de produtos de outras marcas  
   - Prestação de serviços  
   - Software e/ou tecnologia  

6. **Há quanto tempo sua marca está em utilização?**  
   - Menos de 1 ano  
   - 1 a 3 anos  
   - 3 a 5 anos  
   - Mais de 5 anos  

---

### Perguntas numéricas para cálculo do valuation
7. **Qual a sua previsão de faturamento para 2025?** *(R$)*  
8. **Qual a sua meta de faturamento para 2026?** *(R$)*  
9. **Qual a sua margem de lucro estimada?** *(%) *

---

### Perguntas com pontuação

10. **O quanto a sua marca é reconhecida no mercado?**  
- Quase ninguém → **1**  
- Algumas pessoas da minha rede → **2**  
- Pessoas do meu segmento já reconhecem → **3**  
- Bastante conhecida, sendo uma das principais do meu mercado → **4**  

11. **Sua marca está presente em quais canais digitais?** *(múltipla escolha)*  
- Apenas WhatsApp ou Instagram → **1**  
- Redes sociais + Site institucional → **2**  
- Redes + Site otimizado + Email Marketing → **3**  
- Tudo acima + Mídia paga ativa → **4**  

12. **Qual a quantidade total de seguidores sua marca tem nas redes sociais?** *(Instagram, TikTok, X, YouTube etc.)*  
- **Pontuação = número de seguidores ÷ 10.000 + 1**  
  - Ex: 2.800 seguidores → 1,28 pontos  

13. **Sua empresa possui uma base ativa de clientes?**  
- Ainda estou conquistando os primeiros → **1**  
- Já temos uma carteira razoável → **2**  
- Temos clientes recorrentes e fidelizados → **3**  
- Temos base ativa e evangelizadores da marca → **4**  

14. **Qual foi o processo de desenvolvimento da sua marca?**  
- Criei por conta própria ou com alguém da família → **1**  
- Fiz um logo simples com designer → **2**  
- Fiz com agência/designer especialista em branding → **3**  

15. **Sua marca já está registrada no INPI?**  
- Não → **0**  
- Pedido em andamento → **1**  
- Sim, já possuo o certificado de registro → **4**  

16. **Quanto já foi investido em branding e marketing ao longo do tempo?**  
- Menos de R$ 1.000 → **1**  
- R$ 1.000 a R$ 10.000 → **2**  
- R$ 10.000 a R$ 50.000 → **3**  
- R$ 50.000 a R$ 100.000 → **4**  
- Acima de R$ 100.000 → **5**  

---

## 2️⃣ Cálculo do Faturamento Projetado

1. **Ano 1 =** resposta da Pergunta 7  
2. **Ano 2 =** resposta da Pergunta 8  
3. **Fator de Crescimento Inicial (FCI):**  
   ```
   FCI = Ano2 / Ano1
   ```
4. **Ano 3 = Ano 2 × (1 + FCI)**  
5. **A partir do Ano 4 em diante:**  
   ```
   FatorDesaceleração = FCI × 0.6666
   AnoX = Ano(X-1) × (1 + FatorDesaceleração)
   ```

---

## 3️⃣ Cálculo do Lucro

1. **Lucro Anual = Faturamento × Margem de Lucro (%)**  
2. **Lucro Total 10 anos = Soma dos 10 anos**  

---

## 4️⃣ Valuation da Marca

1. **Pontuação total = soma de todas as perguntas pontuáveis**  
2. **Obter percentual correspondente na tabela de pontuação**  
3. **Valor da Marca = Lucro Total 10 anos × Percentual**

---

## 5️⃣ Tabela de Pontuação x Percentual

| Pontuação | Percentual |
|----------|-----------|
| 0        | 0,0069    |
| 1        | 0,0138    |
| 2        | 0,0207    |
| 3        | 0,0276    |
| 4        | 0,0345    |
| 5        | 0,0414    |
| 6        | 0,0483    |
| 7        | 0,0552    |
| 8        | 0,0621    |
| 9        | 0,0690    |
| 10       | 0,0759    |
| 11       | 0,0828    |
| 12       | 0,0897    |
| 13       | 0,0966    |
| 14       | 0,1034    |
| 15       | 0,1103    |
| 16       | 0,1172    |
| 17       | 0,1241    |
| 18       | 0,1310    |
| 19       | 0,1379    |
| 20       | 0,1448    |
| 21       | 0,1517    |
| 22       | 0,1586    |
| 23       | 0,1655    |
| 24       | 0,1724    |
| 25       | 0,1793    |
| 26       | 0,1862    |
| 27       | 0,1931    |
| 28       | 0,2000    |
| 29       | 0,2069    |
| 30       | 0,2138    |
| 31       | 0,2207    |
| 32       | 0,2276    |
| 33       | 0,2345    |

---

## 6️⃣ Objeto TypeScript para Map de Pontuação x Percentual

```ts
export const pontuacaoFatores: Record<number, number> = {
  0: 0.0069,
  1: 0.0138,
  2: 0.0207,
  3: 0.0276,
  4: 0.0345,
  5: 0.0414,
  6: 0.0483,
  7: 0.0552,
  8: 0.0621,
  9: 0.0690,
  10: 0.0759,
  11: 0.0828,
  12: 0.0897,
  13: 0.0966,
  14: 0.1034,
  15: 0.1103,
  16: 0.1172,
  17: 0.1241,
  18: 0.1310,
  19: 0.1379,
  20: 0.1448,
  21: 0.1517,
  22: 0.1586,
  23: 0.1655,
  24: 0.1724,
  25: 0.1793,
  26: 0.1862,
  27: 0.1931,
  28: 0.2000,
  29: 0.2069,
  30: 0.2138,
  31: 0.2207,
  32: 0.2276,
  33: 0.2345,
};
```
