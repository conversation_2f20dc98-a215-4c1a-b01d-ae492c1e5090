const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Tamanhos dos ícones necessários
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Função para gerar ícones
async function generateIcons() {
  console.log('🔄 Gerando ícones...');
  
  const sourceIcon = path.join(__dirname, '../public/images/isotipo.png');
  const iconsDir = path.join(__dirname, '../public/icons');
  
  // Criar diretório se não existir
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }
  
  for (const size of iconSizes) {
    try {
      await sharp(sourceIcon)
        .resize(size, size)
        .png()
        .toFile(path.join(iconsDir, `icon-${size}x${size}.png`));
      
      console.log(`✅ Ícone ${size}x${size} gerado`);
    } catch (error) {
      console.error(`❌ Erro ao gerar ícone ${size}x${size}:`, error);
    }
  }
}

// Função para gerar splash screens
async function generateSplashScreens() {
  console.log('🔄 Gerando splash screens...');
  
  const sourceIcon = path.join(__dirname, '../public/images/isotipo.png');
  const splashDir = path.join(__dirname, '../public/icons');
  
  const splashScreens = [
    { name: 'splash-iphone-x', width: 1125, height: 2436 },
    { name: 'splash-iphone-xr', width: 828, height: 1792 },
    { name: 'splash-iphone-xs-max', width: 1242, height: 2688 },
    { name: 'splash-iphone-8', width: 750, height: 1334 },
    { name: 'splash-iphone-8-plus', width: 1242, height: 2208 },
    { name: 'splash-ipad', width: 1536, height: 2048 },
    { name: 'splash-ipad-pro', width: 2048, height: 2732 }
  ];
  
  for (const screen of splashScreens) {
    try {
      // Criar splash screen com fundo verde e ícone centralizado
      const { width, height } = screen;
      const iconSize = Math.round(Math.min(width, height) * 0.2); // 20% do tamanho menor, arredondado
      
      await sharp({
        create: {
          width,
          height,
          channels: 4,
          background: { r: 56, g: 192, b: 81, alpha: 1 } // #38C051
        }
      })
      .composite([
        {
          input: await sharp(sourceIcon)
            .resize(iconSize, iconSize)
            .png()
            .toBuffer(),
          gravity: 'center'
        }
      ])
      .png()
      .toFile(path.join(splashDir, `${screen.name}.png`));
      
      console.log(`✅ Splash screen ${screen.name} gerado`);
    } catch (error) {
      console.error(`❌ Erro ao gerar splash screen ${screen.name}:`, error);
    }
  }
}

// Função para gerar screenshots mock
async function generateScreenshots() {
  console.log('🔄 Gerando screenshots...');
  
  const screenshotsDir = path.join(__dirname, '../public/screenshots');
  
  // Criar diretório se não existir
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
  
  // Screenshot desktop
  try {
    await sharp({
      create: {
        width: 1280,
        height: 720,
        channels: 4,
        background: { r: 56, g: 192, b: 81, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#38C051"/>
            <text x="50%" y="50%" text-anchor="middle" fill="white" font-family="Arial" font-size="48">
              Sua Marca Tem Valor - Desktop
            </text>
          </svg>
        `),
        top: 0,
        left: 0
      }
    ])
    .png()
    .toFile(path.join(screenshotsDir, 'desktop-1.png'));
    
    console.log('✅ Screenshot desktop gerado');
  } catch (error) {
    console.error('❌ Erro ao gerar screenshot desktop:', error);
  }
  
  // Screenshot mobile
  try {
    await sharp({
      create: {
        width: 390,
        height: 844,
        channels: 4,
        background: { r: 56, g: 192, b: 81, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="390" height="844" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#38C051"/>
            <text x="50%" y="50%" text-anchor="middle" fill="white" font-family="Arial" font-size="24">
              Sua Marca Tem Valor - Mobile
            </text>
          </svg>
        `),
        top: 0,
        left: 0
      }
    ])
    .png()
    .toFile(path.join(screenshotsDir, 'mobile-1.png'));
    
    console.log('✅ Screenshot mobile gerado');
  } catch (error) {
    console.error('❌ Erro ao gerar screenshot mobile:', error);
  }
}

// Função principal
async function main() {
  console.log('🚀 Iniciando geração de assets PWA...');
  
  try {
    await generateIcons();
    await generateSplashScreens();
    await generateScreenshots();
    
    console.log('🎉 Todos os assets PWA foram gerados com sucesso!');
  } catch (error) {
    console.error('❌ Erro durante a geração:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { generateIcons, generateSplashScreens, generateScreenshots }; 