import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { dadosPessoaisSchema, valorMonetarioSchema, porcentagemSchema } from '@/lib/validation'

// 🧹 Função para limpar dados antigos do localStorage
const limparDadosAntigos = () => {
  if (typeof window !== 'undefined') {
    const storageKey = 'quiz-storage'
    const dadosAntigos = localStorage.getItem(storageKey)
    
    if (dadosAntigos) {
      try {
        const parsed = JSON.parse(dadosAntigos)
        
        // Se tem respostas ou currentStep > 0, limpar tudo
        if (parsed.state && (parsed.state.respostas || parsed.state.currentStep > 0)) {
          console.log('🧹 DETECTADO: Dados antigos no localStorage com respostas/currentStep')
          console.log('🧹 LIMPANDO localStorage completamente...')
          localStorage.removeItem(storageKey)
          console.log('✅ localStorage limpo com sucesso!')
        }
      } catch (error) {
        console.log('⚠️ Erro ao verificar localStorage:', error)
      }
    }
  }
}

// Executar limpeza na inicialização
limparDadosAntigos()

// Tipos para respostas baseados no schema Prisma
interface RespostaRadio {
  tipo: 'RADIO'
  opcaoRespostaId: string
  valor: number // Valor da opção para cálculo
}

interface RespostaNumber {
  tipo: 'NUMBER'
  valorNumerico: number
}

interface RespostaCheckbox {
  tipo: 'CHECKBOX'
  opcaoRespostaIds: string[]
  valores: number[] // valores correspondentes às opções selecionadas
}

type Resposta = RespostaRadio | RespostaNumber | RespostaCheckbox

interface QuizState {
  // Dados pessoais
  nomeMarca: string
  nome: string
  whatsapp: string
  email: string
  
  // Respostas do questionário - usando estrutura do schema Prisma
  respostas: Record<string, Resposta>
  
  // Estado da aplicação
  currentStep: number
  isTransitioning: boolean
  
  // Actions
  setDadosPessoais: (dados: Partial<Pick<QuizState, 'nomeMarca' | 'nome' | 'whatsapp' | 'email'>>) => void
  setRespostaRadio: (perguntaId: string, opcaoRespostaId: string, valor: number) => void
  setRespostaNumber: (perguntaId: string, valorNumerico: number) => void
  setRespostaCheckbox: (perguntaId: string, opcaoRespostaId: string, valor: number, selected: boolean) => void
  setCurrentStep: (step: number) => void
  setIsTransitioning: (transitioning: boolean) => void
  getResposta: (perguntaId: string) => Resposta | undefined
  getRespostaValor: (perguntaId: string) => number | undefined // Para cálculos
  getRespostaOpcaoId: (perguntaId: string) => string | undefined // Para RADIO
  resetQuiz: () => void
  getAllRespostas: () => Record<string, Resposta>
  getAllDados: () => Omit<QuizState, 'setDadosPessoais' | 'setRespostaRadio' | 'setRespostaNumber' | 'setRespostaCheckbox' | 'setCurrentStep' | 'setIsTransitioning' | 'getResposta' | 'getRespostaValor' | 'getRespostaOpcaoId' | 'resetQuiz' | 'getAllRespostas' | 'getAllDados' | 'validateDadosPessoais' | 'validateResposta'>
  validateDadosPessoais: (dados: Partial<Pick<QuizState, 'nomeMarca' | 'nome' | 'whatsapp' | 'email'>>) => { success: boolean; errors?: Record<string, string> }
  validateResposta: (perguntaId: string, valor: string | number | string[], tipo: 'NUMBER' | 'RADIO' | 'CHECKBOX') => { success: boolean; error?: string }
}

export const useQuizStore = create<QuizState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      nomeMarca: '',
      nome: '',
      whatsapp: '',
      email: '',
      respostas: {},
      currentStep: 0,
      isTransitioning: false,
      
      // Actions
      setDadosPessoais: (dados) => {
        console.log('🔍 DEBUG setDadosPessoais:', dados);
        set(dados);
      },
      
      setRespostaRadio: (perguntaId, opcaoRespostaId, valor) => {
        console.log('🔍 DEBUG setRespostaRadio:', { perguntaId, opcaoRespostaId, valor });
        console.log('🔍 DEBUG Estado anterior:', get().respostas);
        
        set((state) => {
          const newState = {
            respostas: {
              ...state.respostas,
              [perguntaId]: {
                tipo: 'RADIO' as const,
                opcaoRespostaId,
                valor
              }
            }
          };
          
          console.log('🔍 DEBUG Novo estado RADIO:', newState.respostas);
          return newState;
        });
      },
      
      setRespostaNumber: (perguntaId, valorNumerico) => {
        console.log('🔍 DEBUG setRespostaNumber:', { perguntaId, valorNumerico });
        console.log('🔍 DEBUG Estado anterior:', get().respostas);
        
        set((state) => {
          const newState = {
            respostas: {
              ...state.respostas,
              [perguntaId]: {
                tipo: 'NUMBER' as const,
                valorNumerico
              }
            }
          };
          
          console.log('🔍 DEBUG Novo estado NUMBER:', newState.respostas);
          return newState;
        });
      },

      setRespostaCheckbox: (perguntaId, opcaoRespostaId, valor, selected) => {
        console.log('🔍 DEBUG setRespostaCheckbox:', { perguntaId, opcaoRespostaId, valor, selected });
        set((state) => {
          const current = state.respostas[perguntaId]
          let opcaoRespostaIds: string[] = []
          let valores: number[] = []
          if (current?.tipo === 'CHECKBOX') {
            opcaoRespostaIds = [...current.opcaoRespostaIds]
            valores = [...current.valores]
          }

          const idx = opcaoRespostaIds.indexOf(opcaoRespostaId)
          if (selected) {
            if (idx === -1) {
              opcaoRespostaIds.push(opcaoRespostaId)
              valores.push(valor)
            }
          } else {
            if (idx !== -1) {
              opcaoRespostaIds.splice(idx, 1)
              valores.splice(idx, 1)
            }
          }

          return {
            respostas: {
              ...state.respostas,
              [perguntaId]: {
                tipo: 'CHECKBOX' as const,
                opcaoRespostaIds,
                valores
              }
            }
          }
        })
      },
      
      setCurrentStep: (step) => {
        console.log('🔍 DEBUG setCurrentStep:', step);
        set({ currentStep: step });
      },
      
      setIsTransitioning: (transitioning) => {
        set({ isTransitioning: transitioning });
      },
      
      getResposta: (perguntaId) => {
        const respostas = get().respostas;
        const resposta = respostas[perguntaId];
        console.log('🔍 DEBUG getResposta:', { perguntaId, resposta });
        return resposta;
      },
      
      getRespostaValor: (perguntaId) => {
        const resposta = get().respostas[perguntaId];
        if (resposta?.tipo === 'RADIO') {
          return resposta.valor;
        } else if (resposta?.tipo === 'NUMBER') {
          return resposta.valorNumerico;
        } else if (resposta?.tipo === 'CHECKBOX') {
          return resposta.valores.reduce((sum, v) => sum + v, 0)
        }
        return undefined;
      },
      
      getRespostaOpcaoId: (perguntaId) => {
        const resposta = get().respostas[perguntaId];
        return resposta?.tipo === 'RADIO' ? resposta.opcaoRespostaId : undefined;
      },
      
      resetQuiz: () => {
        console.log('🔍 DEBUG resetQuiz - LIMPANDO FORMULÁRIO');
        set({
          nomeMarca: '',
          nome: '',
          whatsapp: '',
          email: '',
          respostas: {},
          currentStep: 0,
          isTransitioning: false
        });
        console.log('✅ Formulário limpo com sucesso!');
      },
      
      getAllRespostas: () => {
        const respostas = get().respostas;
        console.log('🔍 DEBUG getAllRespostas:', respostas);
        return respostas;
      },
      
      getAllDados: () => {
        const state = get();
        const dados = {
          nomeMarca: state.nomeMarca,
          nome: state.nome,
          whatsapp: state.whatsapp,
          email: state.email,
          respostas: state.respostas,
          currentStep: state.currentStep,
          isTransitioning: state.isTransitioning
        };
        console.log('🔍 DEBUG getAllDados:', dados);
        return dados;
      },
      
      validateDadosPessoais: (dados) => {
        console.log('🔍 DEBUG validateDadosPessoais:', dados);
        
        try {
          // Validar campo individual em vez do schema completo
          const fieldName = Object.keys(dados)[0];
          const fieldValue = dados[fieldName as keyof typeof dados];
          
          console.log('🔍 DEBUG Validando campo:', { fieldName, fieldValue });
          
          if (fieldName === 'nomeMarca') {
            dadosPessoaisSchema.shape.nomeMarca.parse(fieldValue);
          } else if (fieldName === 'nome') {
            dadosPessoaisSchema.shape.nome.parse(fieldValue);
          } else if (fieldName === 'whatsapp') {
            dadosPessoaisSchema.shape.whatsapp.parse(fieldValue);
          } else if (fieldName === 'email') {
            dadosPessoaisSchema.shape.email.parse(fieldValue);
          }
          
          console.log('✅ Validação passou para:', fieldName);
          return { success: true };
        } catch (error: any) {
          console.log('❌ Validação falhou:', error);
          
          // 🎯 CORREÇÃO: Pegar apenas a primeira mensagem de erro
          let errorMessage = 'Campo inválido';
          
          console.log('🔍 DEBUG error.errors:', error.errors);
          console.log('🔍 DEBUG typeof error.errors:', typeof error.errors);
          
          if (error.errors) {
            let errorsArray;
            
            // Se error.errors é uma string (JSON), fazer parse
            if (typeof error.errors === 'string') {
              try {
                errorsArray = JSON.parse(error.errors);
                console.log('🔍 DEBUG errorsArray após parse:', errorsArray);
              } catch (parseError) {
                console.log('❌ Erro ao fazer parse do JSON:', parseError);
                errorsArray = [];
              }
            } else if (Array.isArray(error.errors)) {
              errorsArray = error.errors;
            } else {
              errorsArray = [];
            }
            
            // Pegar apenas a primeira mensagem de erro
            if (errorsArray && errorsArray.length > 0) {
              errorMessage = errorsArray[0].message;
              console.log('🔍 DEBUG Primeira mensagem de erro:', errorMessage);
            }
          } else if (error.message) {
            errorMessage = error.message;
          }
          
          const fieldName = Object.keys(dados)[0];
          const errors: Record<string, string> = {
            [fieldName]: errorMessage
          };
          
          console.log('🔍 DEBUG Erro final retornado:', errors);
          return { success: false, errors };
        }
      },
      
      validateResposta: (perguntaId, valor, tipo) => {
        if (tipo === 'NUMBER') {
          try {
            if (typeof valor === 'string') {
              valorMonetarioSchema.parse(parseFloat(valor));
            } else {
              valorMonetarioSchema.parse(valor);
            }
            return { success: true };
          } catch (error: any) {
            return { success: false, error: error.message };
          }
        }
        if (tipo === 'CHECKBOX') {
          // Para checkbox, valor esperado é um array com pelo menos 1 item
          if (Array.isArray(valor) && valor.length > 0) return { success: true }
          return { success: false, error: 'Selecione pelo menos uma opção' }
        }
        
        // Para RADIO, apenas verificar se tem valor
        if (!valor || valor === '') {
          return { success: false, error: 'Esta pergunta é obrigatória' };
        }
        
        return { success: true };
      }
    }),
    {
      name: 'quiz-storage',
      // 🎯 CRÍTICO: Persistir APENAS dados pessoais para facilitar preenchimento
      // NUNCA persistir respostas ou currentStep para garantir independência entre leads
      partialize: (state) => ({
        // ✅ Persistir apenas dados pessoais
        nomeMarca: state.nomeMarca,
        nome: state.nome,
        whatsapp: state.whatsapp,
        email: state.email,
        
        // ❌ NUNCA persistir:
        // - respostas: {}
        // - currentStep: 0
        // - isTransitioning: false
      }),
      onRehydrateStorage: () => (state) => {
        console.log('🔄 ZUSTAND: Restaurando APENAS dados pessoais do localStorage...');
        if (state) {
          console.log('📊 Dados pessoais restaurados:', {
            nomeMarca: state.nomeMarca,
            nome: state.nome,
            whatsapp: state.whatsapp,
            email: state.email,
            // ✅ respostas e currentStep sempre vazios/zerados
            respostasCount: Object.keys(state.respostas || {}).length,
            currentStep: state.currentStep || 0
          });
          
          // 🧹 Garantir que respostas e currentStep estejam limpos
          if (state.respostas && Object.keys(state.respostas).length > 0) {
            console.log('🧹 Limpando respostas antigas do localStorage...');
            state.respostas = {};
          }
          if (state.currentStep && state.currentStep > 0) {
            console.log('🧹 Resetando currentStep para 0...');
            state.currentStep = 0;
          }
        }
      }
    }
  )
) 